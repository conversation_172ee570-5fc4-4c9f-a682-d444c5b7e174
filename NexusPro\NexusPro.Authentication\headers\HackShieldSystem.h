/*
 * HackShieldSystem.h
 * 
 * Header for HackShield anti-cheat system integration
 * Handles session management, verification, and response processing
 */

#pragma once
#ifndef HACKSHIELDSYSTEM_H
#define HACKSHIELDSYSTEM_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace AntiCheat {

// Forward declarations
class CHackShieldExSystem;
class CNationSettingManager;
struct HACKSHIELD_PARAM_ANTICPU;
struct CHackShieldParam;

/**
 * HackShield session first verification
 * @param this_ptr Pointer to CHackShieldExSystem object
 * @param session_id Session identifier
 * @param param Verification parameters
 * @return Verification result
 */
bool __fastcall HackShieldSessionFirstVerify(CHackShieldExSystem* this_ptr, int session_id, void* param);

/**
 * Nation setting manager session first verification
 * @param this_ptr Pointer to CNationSettingManager object
 * @param session_id Session identifier
 * @param param Verification parameters
 * @return Verification result
 */
bool __fastcall NationSessionFirstVerify(CNationSettingManager* this_ptr, int session_id, void* param);

/**
 * General session first verification check
 * @param this_ptr Pointer to verification object
 * @param session_id Session identifier
 * @param param Verification parameters
 * @return Verification result
 */
bool __fastcall OnCheckSessionFirstVerify(void* this_ptr, int session_id, void* param);

/**
 * HackShield connect session handler
 * @param this_ptr Pointer to CHackShieldExSystem object
 * @param session_id Session identifier
 */
void __fastcall OnConnectSession(CHackShieldExSystem* this_ptr, int session_id);

/**
 * Nation setting manager connect session handler
 * @param this_ptr Pointer to CNationSettingManager object
 * @param session_id Session identifier
 */
void __fastcall NationConnectSession(CNationSettingManager* this_ptr, int session_id);

/**
 * HackShield disconnect session handler
 * @param this_ptr Pointer to CHackShieldExSystem object
 * @param session_id Session identifier
 */
void __fastcall OnDisconnectSession(CHackShieldExSystem* this_ptr, int session_id);

/**
 * Nation setting manager disconnect session handler
 * @param this_ptr Pointer to CNationSettingManager object
 * @param session_id Session identifier
 */
void __fastcall NationDisconnectSession(CNationSettingManager* this_ptr, int session_id);

/**
 * HackShield loop session handler
 * @param this_ptr Pointer to CHackShieldExSystem object
 * @param session_id Session identifier
 */
void __fastcall OnLoopSession(CHackShieldExSystem* this_ptr, int session_id);

/**
 * HackShield receive session handler
 * @param this_ptr Pointer to HACKSHIELD_PARAM_ANTICPU object
 * @param data Received data pointer
 * @param param HackShield parameters
 * @return Processing result
 */
bool __fastcall HackShieldRecvSession(HACKSHIELD_PARAM_ANTICPU* this_ptr, void* data, CHackShieldParam* param);

/**
 * HackShield client checksum response handler
 * @param this_ptr Pointer to HACKSHIELD_PARAM_ANTICPU object
 * @param checksum_data Checksum response data
 * @return Processing result
 */
bool __fastcall HackShieldClientCheckSumResponse(HACKSHIELD_PARAM_ANTICPU* this_ptr, void* checksum_data);

/**
 * HackShield client CRC response handler
 * @param this_ptr Pointer to HACKSHIELD_PARAM_ANTICPU object
 * @param crc_data CRC response data
 * @return Processing result
 */
bool __fastcall HackShieldClientCrcResponse(HACKSHIELD_PARAM_ANTICPU* this_ptr, void* crc_data);

/**
 * HackShield server checksum request handler
 * @param this_ptr Pointer to HACKSHIELD_PARAM_ANTICPU object
 * @param request_data Checksum request data
 * @return Processing result
 */
bool __fastcall HackShieldServerCheckSumRequest(HACKSHIELD_PARAM_ANTICPU* this_ptr, void* request_data);

} // namespace AntiCheat
} // namespace Authentication
} // namespace RFOnline

#endif // HACKSHIELDSYSTEM_H
