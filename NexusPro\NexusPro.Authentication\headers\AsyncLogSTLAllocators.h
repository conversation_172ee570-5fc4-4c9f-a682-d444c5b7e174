/*
 * AsyncLogSTLAllocators.h
 * 
 * Header for STL allocator constructors and functions for AsyncLog containers
 * Restored from original decompiled RF Online authentication source
 */

#pragma once
#ifndef ASYNCLOGSTLALLOCATORS_H
#define ASYNCLOGSTLALLOCATORS_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Logging {

/**
 * STL allocator copy constructor for AsyncLog pair containers
 * @param this_ptr Pointer to allocator object
 * @param other_ptr Pointer to source allocator object
 */
void __fastcall AsyncLogPairAllocatorCopyConstructor(void* this_ptr, void* other_ptr);

/**
 * STL allocator default constructor for AsyncLog pair containers
 * @param this_ptr Pointer to allocator object
 */
void __fastcall AsyncLogPairAllocatorDefaultConstructor(void* this_ptr);

/**
 * STL allocator constructor for AsyncLog iterator containers
 * @param this_ptr Pointer to allocator object
 */
void __fastcall AsyncLogIteratorAllocatorConstructor(void* this_ptr);

/**
 * STL list begin iterator for AsyncLog containers
 * @param this_ptr Pointer to list object
 * @param result_ptr Pointer to result iterator object
 * @return Pointer to iterator result
 */
void* __fastcall AsyncLogListBeginIterator(void* this_ptr, void* result_ptr);

/**
 * STL list end iterator for AsyncLog containers
 * @param this_ptr Pointer to list object
 * @param result_ptr Pointer to result iterator object
 * @return Pointer to iterator result
 */
void* __fastcall AsyncLogListEndIterator(void* this_ptr, void* result_ptr);

} // namespace Logging
} // namespace Authentication
} // namespace RFOnline

#endif // ASYNCLOGSTLALLOCATORS_H
