/*
 * InvalidationFunctions.h
 * 
 * Header for resource invalidation and cleanup functions
 * Restored from original decompiled RF Online authentication source
 */

#pragma once
#ifndef INVALIDATIONFUNCTIONS_H
#define INVALIDATIONFUNCTIONS_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Graphics {

// Forward declarations for graphics classes
class Sky;
class Sun;
class CR3Font;

/**
 * Invalidate nature rendering objects (sky and sun)
 */
void CN_InvalidateNature(void);

/**
 * D3D R3 device invalidation
 * @return Status code for device invalidation
 */
uint32_t D3D_R3InvalidateDevice(void);

/**
 * Invalidate device objects for R3 font
 * @param this_ptr Pointer to CR3Font object
 * @return Success/failure result
 */
uint32_t __fastcall InvalidateR3FontDeviceObjects(CR3Font* this_ptr);

/**
 * Invalidate sky rendering objects
 * @param this_ptr Pointer to Sky object
 */
void __fastcall InvalidateSky(Sky* this_ptr);

/**
 * Invalidate sun rendering objects
 * @param this_ptr Pointer to Sun object
 */
void __fastcall InvalidateSun(Sun* this_ptr);

/**
 * Core R3 rendering device invalidation
 * @return Success/failure result
 */
uint32_t R3InvalidateDevice(void);

} // namespace Graphics
} // namespace Authentication
} // namespace RFOnline

#endif // INVALIDATIONFUNCTIONS_H
