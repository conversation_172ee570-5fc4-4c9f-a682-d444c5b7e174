/*
 * DSAValidation.h
 * 
 * Header for DSA (Digital Signature Algorithm) validation functions
 * Restored from original decompiled RF Online authentication source
 */

#pragma once
#ifndef DSAVALIDATION_H
#define DSAVALIDATION_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Crypto {

/**
 * DSA Private Key Validation
 * @param a1 Private key implementation pointer
 * @param a2 Random number generator pointer
 * @param a3 Validation flags
 * @return Validation result (1 = valid, 0 = invalid)
 */
char __fastcall ValidateDSAPrivateKey(__int64 a1, __int64 a2, unsigned int a3);

/**
 * DSA Public Key Validation
 * @param a1 Public key implementation pointer
 * @param a2 Random number generator pointer
 * @param a3 Validation flags
 * @return Validation result (1 = valid, 0 = invalid)
 */
char __fastcall ValidateDSAPublicKey(__int64 a1, __int64 a2, unsigned int a3);

/**
 * DSA Group Parameters Validation
 * @param a1 Group parameters pointer
 * @param a2 Random number generator pointer
 * @param a3 Validation flags
 * @return Validation result (1 = valid, 0 = invalid)
 */
char __fastcall ValidateDSAGroup(__int64 a1, __int64 a2, unsigned int a3);

} // namespace Crypto
} // namespace Authentication
} // namespace RFOnline

#endif // DSAVALIDATION_H
