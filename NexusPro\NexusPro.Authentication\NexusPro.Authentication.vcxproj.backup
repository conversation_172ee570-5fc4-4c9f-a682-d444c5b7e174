<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{49872A69-4E44-481A-831F-CBDC9AEF1F1B}</ProjectGuid>
    <RootNamespace>NexusProAuthentication</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>NexusPro.Authentication</ProjectName>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  
  <ImportGroup Label="Shared">
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <PropertyGroup Label="UserMacros" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Debug\</OutDir>
    <IntDir>$(SolutionDir)obj\Debug\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Release\</OutDir>
    <IntDir>$(SolutionDir)obj\Release\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemGroup>
    <ClCompile Include="source\test_build.cpp" />
    <ClCompile Include="source\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAAEBV0_1403C5F40.cpp" />
    <ClCompile Include="source\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAXZ_1403C35F0.cpp" />
    <ClCompile Include="source\0allocatorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C6C50.cpp" />
    <ClCompile Include="source\0CAsyncLogInfoQEAAXZ_1403BC9F0.cpp" />
    <ClCompile Include="source\0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQEAA_1403C7590.cpp" />
    <ClCompile Include="source\0hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C17E0.cpp" />
    <ClCompile Include="source\0listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C45B0.cpp" />
    <ClCompile Include="source\0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.cpp" />
    <ClCompile Include="source\0pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C43F0.cpp" />
    <ClCompile Include="source\0pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdQEAAAEBW4_1403C8010.cpp" />
    <ClCompile Include="source\0UpairCBHPEAVCAsyncLogInfostdallocatorPEAU_Node_Li_1403C7E70.cpp" />
    <ClCompile Include="source\0UpairCBHPEAVCAsyncLogInfostdallocatorU_Node_List__1403C7FF0.cpp" />
    <ClCompile Include="source\0UpairCBHPEAVCAsyncLogInfostdallocatorV_Iterator0A_1403C7670.cpp" />
    <ClCompile Include="source\0vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4EF0.cpp" />
    <ClCompile Include="source\0W4ASYNC_LOG_TYPEPEAVCAsyncLogInfopairCBHPEAVCAsyn_1403C7630.cpp" />
    <ClCompile Include="source\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1560.cpp" />
    <ClCompile Include="source\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C5E20.cpp" />
    <ClCompile Include="source\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C1480.cpp" />
    <ClCompile Include="source\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5B90.cpp" />
    <ClCompile Include="source\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C6D10.cpp" />
    <ClCompile Include="source\0_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C2EC0.cpp" />
    <ClCompile Include="source\0_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUless_1403C4520.cpp" />
    <ClCompile Include="source\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2D20.cpp" />
    <ClCompile Include="source\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C42C0.cpp" />
    <ClCompile Include="source\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C5B30.cpp" />
    <ClCompile Include="source\0_List_nodUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C7520.cpp" />
    <ClCompile Include="source\0_List_ptrUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C6F70.cpp" />
    <ClCompile Include="source\0_List_valUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C66F0.cpp" />
    <ClCompile Include="source\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C5EE0.cpp" />
    <ClCompile Include="source\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C74D0.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C5E70.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C73C0.cpp" />
    <ClCompile Include="source\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5DC0.cpp" />
    <ClCompile Include="source\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6D70.cpp" />
    <ClCompile Include="source\0_Vector_valV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6BE0.cpp" />
    <ClCompile Include="source\1CAsyncLogInfoQEAAXZ_1403BCA80.cpp" />
    <ClCompile Include="source\1hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C1170.cpp" />
    <ClCompile Include="source\1listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C3630.cpp" />
    <ClCompile Include="source\1MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140464F50.cpp" />
    <ClCompile Include="source\1pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C1670.cpp" />
    <ClCompile Include="source\1vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C3EE0.cpp" />
    <ClCompile Include="source\1_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1230.cpp" />
    <ClCompile Include="source\1_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C11F0.cpp" />
    <ClCompile Include="source\1_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C1860.cpp" />
    <ClCompile Include="source\1_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C11B0.cpp" />
    <ClCompile Include="source\1_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C44E0.cpp" />
    <ClCompile Include="source\1_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C44A0.cpp" />
    <ClCompile Include="source\1_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C4460.cpp" />
    <ClCompile Include="source\4_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C2DF0.cpp" />
    <ClCompile Include="source\4_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2D80.cpp" />
    <ClCompile Include="source\4_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2CC0.cpp" />
    <ClCompile Include="source\8UpairCBHPEAVCAsyncLogInfostdU01stdYA_NAEBVallocat_1403C7690.cpp" />
    <ClCompile Include="source\8_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400A6CA0.cpp" />
    <ClCompile Include="source\8_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2BE0.cpp" />
    <ClCompile Include="source\8_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7460.cpp" />
    <ClCompile Include="source\9_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400CFE90.cpp" />
    <ClCompile Include="source\9_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2C50.cpp" />
    <ClCompile Include="source\9_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C6E30.cpp" />
    <ClCompile Include="source\AccountServerLoginCMainThreadQEAAXXZ_1401F8140.cpp" />
    <ClCompile Include="source\allocateallocatorU_Node_List_nodUpairCBHPEAVCAsync_1403C7000.cpp" />
    <ClCompile Include="source\allocateallocatorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6CC0.cpp" />
    <ClCompile Include="source\AuthLastCriTicketMiningTicketQEAAHGEEEEZ_1400D01D0.cpp" />
    <ClCompile Include="source\AuthLastMentalTicketMiningTicketQEAAHGEEEEZ_1400CFDB0.cpp" />
    <ClCompile Include="source\AuthMiningTicketCHolyStoneSystemQEAA_NIZ_14027DBD0.cpp" />
    <ClCompile Include="source\auto_trade_login_sellCMgrAvatorItemHistoryQEAAXPEB_14023A3E0.cpp" />
    <ClCompile Include="source\AvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4290.cpp" />
    <ClCompile Include="source\beginlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C3670.cpp" />
    <ClCompile Include="source\beginvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C4F90.cpp" />
    <ClCompile Include="source\begin_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_com_1403C1910.cpp" />
    <ClCompile Include="source\CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEAU__1403198F0.cpp" />
    <ClCompile Include="source\CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU_pa_1403213A0.cpp" />
    <ClCompile Include="source\CallProc_RFOnlineAuthCRFCashItemDatabaseQEAAHAEAU__140482430.cpp" />
    <ClCompile Include="source\CallProc_RFOnlineAuth_JapCRFCashItemDatabaseQEAAHA_1404836A0.cpp" />
    <ClCompile Include="source\capacityvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C6920.cpp" />
    <ClCompile Include="source\clearlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C6250.cpp" />
    <ClCompile Include="source\CN_InvalidateNatureYAXXZ_140504ED0.cpp" />
    <ClCompile Include="source\CompleteLogInCompeteCUnmannedTraderControllerQEAAX_14034EF80.cpp" />
    <ClCompile Include="source\constructallocatorPEAU_Node_List_nodUpairCBHPEAVCA_1403C70A0.cpp" />
    <ClCompile Include="source\constructallocatorUpairCBHPEAVCAsyncLogInfostdstdQ_1403C6EA0.cpp" />
    <ClCompile Include="source\constructallocatorV_Iterator0AlistUpairCBHPEAVCAsy_1403C89B0.cpp" />
    <ClCompile Include="source\C_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2AF0.cpp" />
    <ClCompile Include="source\D3D_R3InvalidateDeviceYAJXZ_14050B040.cpp" />
    <ClCompile Include="source\deallocateallocatorU_Node_List_nodUpairCBHPEAVCAsy_1403C6780.cpp" />
    <ClCompile Include="source\deallocateallocatorV_Iterator0AlistUpairCBHPEAVCAs_1403C6C70.cpp" />
    <ClCompile Include="source\destroyallocatorPEAU_Node_List_nodUpairCBHPEAVCAsy_1403C67D0.cpp" />
    <ClCompile Include="source\destroyallocatorU_Node_List_nodUpairCBHPEAVCAsyncL_1403C7050.cpp" />
    <ClCompile Include="source\destroyallocatorV_Iterator0AlistUpairCBHPEAVCAsync_1403C8A10.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_0_14057B120.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_10_14057B3A0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_11_14057B3E0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_12_14057B420.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_13_14057B460.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_14057B0E0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_14_14057C3D0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_15_14057C410.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_16_14057C450.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_17_14057C490.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_18_14057C4D0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_19_14057C510.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_1_14057B160.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_20_14057C550.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_21_14057C590.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_22_14057C5D0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_23_14057C610.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_24_14057C650.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_25_14057C690.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_26_14057C6D0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_27_14057C710.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_28_14057C750.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_29_14057C790.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_2_14057B1A0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_30_14057C7D0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_31_14057C810.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_3_14057B1E0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_4_14057B220.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_5_14057B260.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_6_14057B2A0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_7_14057B2E0.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_8_14057B320.cpp" />
    <ClCompile Include="source\dtor00__F_afxSessionMapYAXXZ4HA_9_14057B360.cpp" />
    <ClCompile Include="source\D_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B30.cpp" />
    <ClCompile Include="source\D_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4310.cpp" />
    <ClCompile Include="source\endlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpair_1403C36F0.cpp" />
    <ClCompile Include="source\endvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C5000.cpp" />
    <ClCompile Include="source\end_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compa_1403C1990.cpp" />
    <ClCompile Include="source\eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C4790.cpp" />
    <ClCompile Include="source\eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C5FB0.cpp" />
    <ClCompile Include="source\erasevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C5070.cpp" />
    <ClCompile Include="source\E_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B80.cpp" />
    <ClCompile Include="source\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4350.cpp" />
    <ClCompile Include="source\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C7270.cpp" />
    <ClCompile Include="source\fillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C7AA0.cpp" />
    <ClCompile Include="source\find_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_comp_1403C2A70.cpp" />
    <ClCompile Include="source\F_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5BF0.cpp" />
    <ClCompile Include="source\F_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C43A0.cpp" />
    <ClCompile Include="source\GenerateEphemeralKeyPairAuthenticatedKeyAgreementD_1405F6600.cpp" />
    <ClCompile Include="source\GenerateStaticKeyPairAuthenticatedKeyAgreementDoma_1405F65A0.cpp" />
    <ClCompile Include="source\GetCountCAsyncLogInfoQEAAKXZ_1403C16B0.cpp" />
    <ClCompile Include="source\GetDirPathCAsyncLogInfoQEAAPEBDXZ_1403C1630.cpp" />
    <ClCompile Include="source\GetFileNameCAsyncLogInfoQEAAPEBDXZ_1403C16D0.cpp" />
    <ClCompile Include="source\GetOccDialogInfoCDialogMEAAPEAU_AFX_OCC_DIALOG_INF_1404DBD48.cpp" />
    <ClCompile Include="source\GetOccDialogInfoCFormViewMEAAPEAU_AFX_OCC_DIALOG_I_1404DC15C.cpp" />
    <ClCompile Include="source\GetOccDialogInfoCWndMEAAPEAU_AFX_OCC_DIALOG_INFOXZ_1404DBE20.cpp" />
    <ClCompile Include="source\GetTypeNameCAsyncLogInfoQEAAPEBDXZ_1403C1650.cpp" />
    <ClCompile Include="source\H_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5C70.cpp" />
    <ClCompile Include="source\IncreaseCountCAsyncLogInfoQEAAXXZ_1403C16F0.cpp" />
    <ClCompile Include="source\InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.cpp" />
    <ClCompile Include="source\Init_AuthKeyTicketMiningTicketQEAAXXZ_140073BC0.cpp" />
    <ClCompile Include="source\insertlistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C3760.cpp" />
    <ClCompile Include="source\insertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C76B0.cpp" />
    <ClCompile Include="source\insert_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_co_1403C1A10.cpp" />
    <ClCompile Include="source\InvalidateDeviceObjectsCR3FontQEAAJXZ_140528820.cpp" />
    <ClCompile Include="source\InvalidateSkySkyQEAAXXZ_1405229B0.cpp" />
    <ClCompile Include="source\InvalidateSunSunQEAAXXZ_1405221E0.cpp" />
    <ClCompile Include="source\IsLogInStateCUnmannedTraderUserInfoQEAA_NXZ_140366F20.cpp" />
    <ClCompile Include="source\j_0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAAEB_14000EF34.cpp" />
    <ClCompile Include="source\j_0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAXZ_14000C0BD.cpp" />
    <ClCompile Include="source\j_0allocatorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_140012E9A.cpp" />
    <ClCompile Include="source\j_0CAsyncLogInfoQEAAXZ_14000E4F8.cpp" />
    <ClCompile Include="source\j_0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQE_140013B1F.cpp" />
    <ClCompile Include="source\j_0hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHs_14000DFBC.cpp" />
    <ClCompile Include="source\j_0listUpairCBHPEAVCAsyncLogInfostdVallocatorUpair_140011635.cpp" />
    <ClCompile Include="source\j_0MessageAuthenticationCodeImplVHMAC_BaseCryptoPP_14000FCA4.cpp" />
    <ClCompile Include="source\j_0pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1400064BA.cpp" />
    <ClCompile Include="source\j_0pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdQEAAAEB_140007469.cpp" />
    <ClCompile Include="source\j_0UpairCBHPEAVCAsyncLogInfostdallocatorPEAU_Node__140004836.cpp" />
    <ClCompile Include="source\j_0UpairCBHPEAVCAsyncLogInfostdallocatorU_Node_Lis_140013516.cpp" />
    <ClCompile Include="source\j_0UpairCBHPEAVCAsyncLogInfostdallocatorV_Iterator_140002CB1.cpp" />
    <ClCompile Include="source\j_0vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140002C1B.cpp" />
    <ClCompile Include="source\j_0W4ASYNC_LOG_TYPEPEAVCAsyncLogInfopairCBHPEAVCAs_14000D4F4.cpp" />
    <ClCompile Include="source\j_0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_140005678.cpp" />
    <ClCompile Include="source\j_0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_14000FC2C.cpp" />
    <ClCompile Include="source\j_0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14000C6B2.cpp" />
    <ClCompile Include="source\j_0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140010E06.cpp" />
    <ClCompile Include="source\j_0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14001213E.cpp" />
    <ClCompile Include="source\j_0_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compa_14001253F.cpp" />
    <ClCompile Include="source\j_0_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUle_14000B91A.cpp" />
    <ClCompile Include="source\j_0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000269E.cpp" />
    <ClCompile Include="source\j_0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000CA54.cpp" />
    <ClCompile Include="source\j_0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140010D1B.cpp" />
    <ClCompile Include="source\j_0_List_nodUpairCBHPEAVCAsyncLogInfostdVallocator_140002DC4.cpp" />
    <ClCompile Include="source\j_0_List_ptrUpairCBHPEAVCAsyncLogInfostdVallocator_1400071CB.cpp" />
    <ClCompile Include="source\j_0_List_valUpairCBHPEAVCAsyncLogInfostdVallocator_1400024CD.cpp" />
    <ClCompile Include="source\j_0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_14000BAE1.cpp" />
    <ClCompile Include="source\j_0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140011847.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140003EC2.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorV_Iterator0AlistUpairCBHP_14000D526.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_140007405.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_14000A8FD.cpp" />
    <ClCompile Include="source\j_0_Vector_valV_Iterator0AlistUpairCBHPEAVCAsyncLo_1400107E4.cpp" />
    <ClCompile Include="source\j_1CAsyncLogInfoQEAAXZ_14000F182.cpp" />
    <ClCompile Include="source\j_1hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHs_14000900C.cpp" />
    <ClCompile Include="source\j_1listUpairCBHPEAVCAsyncLogInfostdVallocatorUpair_140011950.cpp" />
    <ClCompile Include="source\j_1MessageAuthenticationCodeImplVHMAC_BaseCryptoPP_1400097F5.cpp" />
    <ClCompile Include="source\j_1pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_140005970.cpp" />
    <ClCompile Include="source\j_1vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140009B47.cpp" />
    <ClCompile Include="source\j_1_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_14000684D.cpp" />
    <ClCompile Include="source\j_1_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_1400047BE.cpp" />
    <ClCompile Include="source\j_1_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compa_14000839B.cpp" />
    <ClCompile Include="source\j_1_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000788D.cpp" />
    <ClCompile Include="source\j_1_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140006F73.cpp" />
    <ClCompile Include="source\j_1_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140008B52.cpp" />
    <ClCompile Include="source\j_1_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_140012580.cpp" />
    <ClCompile Include="source\j_4_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_140009D6D.cpp" />
    <ClCompile Include="source\j_4_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140001488.cpp" />
    <ClCompile Include="source\j_4_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140005547.cpp" />
    <ClCompile Include="source\j_8UpairCBHPEAVCAsyncLogInfostdU01stdYA_NAEBValloc_14000BDD9.cpp" />
    <ClCompile Include="source\j_8_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_140010A91.cpp" />
    <ClCompile Include="source\j_8_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140012F2B.cpp" />
    <ClCompile Include="source\j_8_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140012F9E.cpp" />
    <ClCompile Include="source\j_9_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400090B1.cpp" />
    <ClCompile Include="source\j_9_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14000C310.cpp" />
    <ClCompile Include="source\j_9_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140006938.cpp" />
    <ClCompile Include="source\j_AccountServerLoginCMainThreadQEAAXXZ_14001102C.cpp" />
    <ClCompile Include="source\j_allocateallocatorU_Node_List_nodUpairCBHPEAVCAsy_140001794.cpp" />
    <ClCompile Include="source\j_allocateallocatorV_Iterator0AlistUpairCBHPEAVCAs_140001E7E.cpp" />
    <ClCompile Include="source\j_AuthLastCriTicketMiningTicketQEAAHGEEEEZ_14000DDAA.cpp" />
    <ClCompile Include="source\j_AuthLastMentalTicketMiningTicketQEAAHGEEEEZ_14000F38A.cpp" />
    <ClCompile Include="source\j_AuthMiningTicketCHolyStoneSystemQEAA_NIZ_140009EBC.cpp" />
    <ClCompile Include="source\j_auto_trade_login_sellCMgrAvatorItemHistoryQEAAXP_140006FAA.cpp" />
    <ClCompile Include="source\j_AvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140002306.cpp" />
    <ClCompile Include="source\j_beginlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1400096E2.cpp" />
    <ClCompile Include="source\j_beginvectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_14000CFE0.cpp" />
    <ClCompile Include="source\j_begin_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_c_1400049AD.cpp" />
    <ClCompile Include="source\j_CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEA_14000D4B3.cpp" />
    <ClCompile Include="source\j_CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU__140001E24.cpp" />
    <ClCompile Include="source\j_CallProc_RFOnlineAuthCRFCashItemDatabaseQEAAHAEA_14000C2F2.cpp" />
    <ClCompile Include="source\j_CallProc_RFOnlineAuth_JapCRFCashItemDatabaseQEAA_14000B334.cpp" />
    <ClCompile Include="source\j_capacityvectorV_Iterator0AlistUpairCBHPEAVCAsync_14000CB58.cpp" />
    <ClCompile Include="source\j_clearlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_140008D82.cpp" />
    <ClCompile Include="source\j_CompleteLogInCompeteCUnmannedTraderControllerQEA_14000E66A.cpp" />
    <ClCompile Include="source\j_constructallocatorPEAU_Node_List_nodUpairCBHPEAV_14000EF6B.cpp" />
    <ClCompile Include="source\j_constructallocatorUpairCBHPEAVCAsyncLogInfostdst_14000ECC8.cpp" />
    <ClCompile Include="source\j_constructallocatorV_Iterator0AlistUpairCBHPEAVCA_140006CB2.cpp" />
    <ClCompile Include="source\j_C_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140009BB0.cpp" />
    <ClCompile Include="source\j_deallocateallocatorU_Node_List_nodUpairCBHPEAVCA_140010910.cpp" />
    <ClCompile Include="source\j_deallocateallocatorV_Iterator0AlistUpairCBHPEAVC_14000176C.cpp" />
    <ClCompile Include="source\j_destroyallocatorPEAU_Node_List_nodUpairCBHPEAVCA_140001C6C.cpp" />
    <ClCompile Include="source\j_destroyallocatorU_Node_List_nodUpairCBHPEAVCAsyn_14000A6EB.cpp" />
    <ClCompile Include="source\j_destroyallocatorV_Iterator0AlistUpairCBHPEAVCAsy_140013A98.cpp" />
    <ClCompile Include="source\j_D_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140003797.cpp" />
    <ClCompile Include="source\j_D_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140002C70.cpp" />
    <ClCompile Include="source\j_endlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_140008A76.cpp" />
    <ClCompile Include="source\j_endvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_140004A57.cpp" />
    <ClCompile Include="source\j_end_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_com_1400029EB.cpp" />
    <ClCompile Include="source\j_eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorU_140003D1E.cpp" />
    <ClCompile Include="source\j_eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorU_14000C559.cpp" />
    <ClCompile Include="source\j_erasevectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_140010C7B.cpp" />
    <ClCompile Include="source\j_E_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140003A80.cpp" />
    <ClCompile Include="source\j_E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140007F54.cpp" />
    <ClCompile Include="source\j_E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000B758.cpp" />
    <ClCompile Include="source\j_fillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140010B90.cpp" />
    <ClCompile Include="source\j_find_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_co_14000F4A2.cpp" />
    <ClCompile Include="source\j_F_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14000A187.cpp" />
    <ClCompile Include="source\j_F_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140011E87.cpp" />
    <ClCompile Include="source\j_GetCountCAsyncLogInfoQEAAKXZ_14000B60E.cpp" />
    <ClCompile Include="source\j_GetDirPathCAsyncLogInfoQEAAPEBDXZ_140011A77.cpp" />
    <ClCompile Include="source\j_GetFileNameCAsyncLogInfoQEAAPEBDXZ_1400112A7.cpp" />
    <ClCompile Include="source\j_GetTypeNameCAsyncLogInfoQEAAPEBDXZ_140003337.cpp" />
    <ClCompile Include="source\j_H_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_14000B6F4.cpp" />
    <ClCompile Include="source\j_IncreaseCountCAsyncLogInfoQEAAXXZ_140007B5D.cpp" />
    <ClCompile Include="source\j_InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKA_14000C3F1.cpp" />
    <ClCompile Include="source\j_Init_AuthKeyTicketMiningTicketQEAAXXZ_140004B5B.cpp" />
    <ClCompile Include="source\j_insertlistUpairCBHPEAVCAsyncLogInfostdVallocator_14000EC2D.cpp" />
    <ClCompile Include="source\j_insertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1400133BD.cpp" />
    <ClCompile Include="source\j_insert_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash__140008F53.cpp" />
    <ClCompile Include="source\j_IsLogInStateCUnmannedTraderUserInfoQEAA_NXZ_140009F3E.cpp" />
    <ClCompile Include="source\j_LoginCBillingIDUEAAXPEAVCUserDBZ_140002B76.cpp" />
    <ClCompile Include="source\j_LoginCBillingJPUEAAXPEAVCUserDBZ_140003FDA.cpp" />
    <ClCompile Include="source\j_LoginCBillingManagerQEAAXPEAVCUserDBZ_140011F54.cpp" />
    <ClCompile Include="source\j_LoginCBillingNULLUEAAXPEAVCUserDBZ_140006D4D.cpp" />
    <ClCompile Include="source\j_LoginCBillingUEAAXPEAVCUserDBZ_14000AFD8.cpp" />
    <ClCompile Include="source\j_LogInCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKE_1400127B5.cpp" />
    <ClCompile Include="source\j_LoginCNormalGuildBattleGuildMemberGUILD_BATTLEQE_14000E304.cpp" />
    <ClCompile Include="source\j_LogInCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1400062BC.cpp" />
    <ClCompile Include="source\j_LogInCNormalGuildBattleManagerGUILD_BATTLEQEAAXH_14001391C.cpp" />
    <ClCompile Include="source\j_LogInControllServerCNetworkEXAEAA_NHPEADZ_14000E8DB.cpp" />
    <ClCompile Include="source\j_LogInWebAgentServerCNetworkEXAEAA_NHPEADZ_1400026FD.cpp" />
    <ClCompile Include="source\j_login_cancel_auto_tradeCMgrAvatorItemHistoryQEAA_140011B71.cpp" />
    <ClCompile Include="source\j_lower_bound_HashV_Hmap_traitsHPEAVCAsyncLogInfoV_1400014EC.cpp" />
    <ClCompile Include="source\j_make_pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdYAA_1400120E4.cpp" />
    <ClCompile Include="source\j_max_sizeallocatorUpairCBHPEAVCAsyncLogInfostdstd_140006AAA.cpp" />
    <ClCompile Include="source\j_max_sizeallocatorV_Iterator0AlistUpairCBHPEAVCAs_1400094B2.cpp" />
    <ClCompile Include="source\j_max_sizelistUpairCBHPEAVCAsyncLogInfostdVallocat_140004098.cpp" />
    <ClCompile Include="source\j_max_sizevectorV_Iterator0AlistUpairCBHPEAVCAsync_14000E9C6.cpp" />
    <ClCompile Include="source\j_NotifyLogInSetBuffCRaceBuffInfoByHolyQuestAEAAXG_14000A1A5.cpp" />
    <ClCompile Include="source\j_OnCheckSession_FirstVerifyCHackShieldExSystemUEA_1400114FA.cpp" />
    <ClCompile Include="source\j_OnCheckSession_FirstVerifyCNationSettingManagerQ_140008558.cpp" />
    <ClCompile Include="source\j_OnCheckSession_FirstVerifyHACKSHEILD_PARAM_ANTIC_140007789.cpp" />
    <ClCompile Include="source\j_OnConnectSessionCHackShieldExSystemUEAAXHZ_14000FEA7.cpp" />
    <ClCompile Include="source\j_OnConnectSessionCNationSettingManagerQEAAXHZ_1400046EC.cpp" />
    <ClCompile Include="source\j_OnDisConnectSessionCHackShieldExSystemUEAAXHZ_1400017DF.cpp" />
    <ClCompile Include="source\j_OnDisConnectSessionCNationSettingManagerQEAAXHZ_14000B2E4.cpp" />
    <ClCompile Include="source\j_OnLoopSessionCHackShieldExSystemUEAAXHZ_1400137FA.cpp" />
    <ClCompile Include="source\j_OnRecvSessionHACKSHEILD_PARAM_ANTICPUEAA_NPEAVCH_140008B89.cpp" />
    <ClCompile Include="source\j_OnRecvSession_ClientCheckSum_ResponseHACKSHEILD__140004CA5.cpp" />
    <ClCompile Include="source\j_OnRecvSession_ClientCrc_ResponseHACKSHEILD_PARAM_14000338C.cpp" />
    <ClCompile Include="source\j_OnRecvSession_ServerCheckSum_RequestHACKSHEILD_P_14000C342.cpp" />
    <ClCompile Include="source\j_resizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_140006C12.cpp" />
    <ClCompile Include="source\j_SendMsg_CurAllUserLoginCBillingIEAAXXZ_140013D0E.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_14000F11E.cpp" />
    <ClCompile Include="source\j_SendMsg_LoginCBillingIDMEAA_NPEAD00FPEAU_SYSTEMT_1400078F1.cpp" />
    <ClCompile Include="source\j_SendMsg_LoginCBillingJPMEAA_NPEAD00FPEAU_SYSTEMT_14001081B.cpp" />
    <ClCompile Include="source\j_SendMsg_LoginCBillingMEAA_NPEAD00FPEAU_SYSTEMTIM_1400040BB.cpp" />
    <ClCompile Include="source\j_SendMsg_LoginCBillingNULLMEAA_NPEAD00FPEAU_SYSTE_1400020D1.cpp" />
    <ClCompile Include="source\j_SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAX_14000DA17.cpp" />
    <ClCompile Include="source\j_Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_14000CA59.cpp" />
    <ClCompile Include="source\j_Set_AuthKeyTicketMiningTicketQEAAXIZ_140002F18.cpp" />
    <ClCompile Include="source\j_sizelistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_140007054.cpp" />
    <ClCompile Include="source\j_sizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_14000AABA.cpp" />
    <ClCompile Include="source\j_size_apex_send_loginQEAAHXZ_140013B97.cpp" />
    <ClCompile Include="source\j_size_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_co_14000896D.cpp" />
    <ClCompile Include="source\j_unchecked_copyPEAV_Iterator0AlistUpairCBHPEAVCAs_14000ECDC.cpp" />
    <ClCompile Include="source\j_unchecked_uninitialized_copyPEAV_Iterator0AlistU_140007C34.cpp" />
    <ClCompile Include="source\j_unchecked_uninitialized_fill_nPEAV_Iterator0Alis_140003B7F.cpp" />
    <ClCompile Include="source\j_UpdateLogFileNameCAsyncLogInfoQEAAXXZ_140006852.cpp" />
    <ClCompile Include="source\j_UpdateLogInCompleteCUnmannedTraderControllerQEAA_14000CDE2.cpp" />
    <ClCompile Include="source\j_Update_TrunkPasswordCUserDBQEAA_NPEADZ_1400018F7.cpp" />
    <ClCompile Include="source\j_ValidateDL_GroupParametersUECPPointCryptoPPCrypt_1400018A2.cpp" />
    <ClCompile Include="source\j_ValidateDL_GroupParametersUECPPointCryptoPPCrypt_140007478.cpp" />
    <ClCompile Include="source\j_ValidateDL_GroupParametersUECPPointCryptoPPCrypt_14000C37E.cpp" />
    <ClCompile Include="source\j_ValidateDL_PrivateKeyImplVDL_GroupParameters_ECV_140009D9F.cpp" />
    <ClCompile Include="source\j_ValidateDL_PublicKeyImplVDL_GroupParameters_ECVE_14000AAEC.cpp" />
    <ClCompile Include="source\j_validatetable_objlua_tinkerQEAA_NXZ_140009525.cpp" />
    <ClCompile Include="source\j_Y_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140003E04.cpp" />
    <ClCompile Include="source\j_Y_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_14000E15B.cpp" />
    <ClCompile Include="source\j__AllocateU_Node_List_nodUpairCBHPEAVCAsyncLogInf_14000E174.cpp" />
    <ClCompile Include="source\j__AllocateV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_140007CE3.cpp" />
    <ClCompile Include="source\j__BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocat_1400123A5.cpp" />
    <ClCompile Include="source\j__BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocat_140012C5B.cpp" />
    <ClCompile Include="source\j__BuyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_140004F11.cpp" />
    <ClCompile Include="source\j__ConstructPEAU_Node_List_nodUpairCBHPEAVCAsyncLo_140009EE4.cpp" />
    <ClCompile Include="source\j__ConstructUpairCBHPEAVCAsyncLogInfostdU12stdYAXP_14000F3A3.cpp" />
    <ClCompile Include="source\j__ConstructV_Iterator0AlistUpairCBHPEAVCAsyncLogI_140007D6F.cpp" />
    <ClCompile Include="source\j__Construct_nvectorV_Iterator0AlistUpairCBHPEAVCA_14000467E.cpp" />
    <ClCompile Include="source\j__Copy_backward_optPEAV_Iterator0AlistUpairCBHPEA_140001271.cpp" />
    <ClCompile Include="source\j__Copy_optPEAV_Iterator0AlistUpairCBHPEAVCAsyncLo_14000AB64.cpp" />
    <ClCompile Include="source\j__DestroyPEAU_Node_List_nodUpairCBHPEAVCAsyncLogI_140001541.cpp" />
    <ClCompile Include="source\j__DestroyU_Node_List_nodUpairCBHPEAVCAsyncLogInfo_14000A9C0.cpp" />
    <ClCompile Include="source\j__DestroyvectorV_Iterator0AlistUpairCBHPEAVCAsync_140002CA2.cpp" />
    <ClCompile Include="source\j__DestroyV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_14000135C.cpp" />
    <ClCompile Include="source\j__Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsync_140001177.cpp" />
    <ClCompile Include="source\j__Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsync_14000AF8D.cpp" />
    <ClCompile Include="source\j__EMessageAuthenticationCodeImplVHMAC_BaseCryptoP_140002B6C.cpp" />
    <ClCompile Include="source\j__EMessageAuthenticationCodeImplVHMAC_BaseCryptoP_14000DE2C.cpp" />
    <ClCompile Include="source\j__EMessageAuthenticationCodeImplVHMAC_BaseCryptoP_140013985.cpp" />
    <ClCompile Include="source\j__FillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_14000F9D9.cpp" />
    <ClCompile Include="source\j__GCAsyncLogInfoQEAAPEAXIZ_14000F1B9.cpp" />
    <ClCompile Include="source\j__Get_iter_from_vec_HashV_Hmap_traitsHPEAVCAsyncL_1400066E5.cpp" />
    <ClCompile Include="source\j__G_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVal_14000DEAE.cpp" />
    <ClCompile Include="source\j__Hashval_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhas_140005F0B.cpp" />
    <ClCompile Include="source\j__IncsizelistUpairCBHPEAVCAsyncLogInfostdVallocat_140004CA0.cpp" />
    <ClCompile Include="source\j__InsertlistUpairCBHPEAVCAsyncLogInfostdVallocato_140013F34.cpp" />
    <ClCompile Include="source\j__InsertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140002D29.cpp" />
    <ClCompile Include="source\j__Insert_nvectorV_Iterator0AlistUpairCBHPEAVCAsyn_140011CD4.cpp" />
    <ClCompile Include="source\j__Iter_catV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_14000AA3D.cpp" />
    <ClCompile Include="source\j__Iter_randomPEAV_Iterator0AlistUpairCBHPEAVCAsyn_140008332.cpp" />
    <ClCompile Include="source\j__Kfn_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareH_140011A90.cpp" />
    <ClCompile Include="source\j__Move_backward_optPEAV_Iterator0AlistUpairCBHPEA_14000B69A.cpp" />
    <ClCompile Include="source\j__Move_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLo_140003251.cpp" />
    <ClCompile Include="source\j__Mynode_Const_iterator0AlistUpairCBHPEAVCAsyncLo_14000F380.cpp" />
    <ClCompile Include="source\j__MyvallistUpairCBHPEAVCAsyncLogInfostdVallocator_140003DAF.cpp" />
    <ClCompile Include="source\j__NextnodelistUpairCBHPEAVCAsyncLogInfostdValloca_140007F90.cpp" />
    <ClCompile Include="source\j__PrevnodelistUpairCBHPEAVCAsyncLogInfostdValloca_1400055BF.cpp" />
    <ClCompile Include="source\j__Ptr_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLog_14001394E.cpp" />
    <ClCompile Include="source\j__SplicelistUpairCBHPEAVCAsyncLogInfostdVallocato_14000E534.cpp" />
    <ClCompile Include="source\j__TidylistUpairCBHPEAVCAsyncLogInfostdVallocatorU_140001555.cpp" />
    <ClCompile Include="source\j__TidyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_140012760.cpp" />
    <ClCompile Include="source\j__UfillvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1400060F5.cpp" />
    <ClCompile Include="source\j__UmovePEAV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_140005F74.cpp" />
    <ClCompile Include="source\j__Unchecked_move_backwardPEAV_Iterator0AlistUpair_14000CF40.cpp" />
    <ClCompile Include="source\j__Unchecked_uninitialized_movePEAV_Iterator0Alist_140005C68.cpp" />
    <ClCompile Include="source\j__Uninit_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyn_14000FCCC.cpp" />
    <ClCompile Include="source\j__Uninit_fill_nPEAV_Iterator0AlistUpairCBHPEAVCAs_14000DAC1.cpp" />
    <ClCompile Include="source\j__Uninit_movePEAV_Iterator0AlistUpairCBHPEAVCAsyn_140011509.cpp" />
    <ClCompile Include="source\j__XlenvectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_14001003C.cpp" />
    <ClCompile Include="source\LoginCBillingIDUEAAXPEAVCUserDBZ_14028E0F0.cpp" />
    <ClCompile Include="source\LoginCBillingJPUEAAXPEAVCUserDBZ_14028E910.cpp" />
    <ClCompile Include="source\LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.cpp" />
    <ClCompile Include="source\LoginCBillingNULLUEAAXPEAVCUserDBZ_14028DBD0.cpp" />
    <ClCompile Include="source\LoginCBillingUEAAXPEAVCUserDBZ_14028CAC0.cpp" />
    <ClCompile Include="source\LogInCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKEPE_1403E0DD0.cpp" />
    <ClCompile Include="source\LoginCNormalGuildBattleGuildMemberGUILD_BATTLEQEAA_1403DFA80.cpp" />
    <ClCompile Include="source\LogInCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1403E4050.cpp" />
    <ClCompile Include="source\LogInCNormalGuildBattleManagerGUILD_BATTLEQEAAXHKK_1403D4360.cpp" />
    <ClCompile Include="source\LogInControllServerCNetworkEXAEAA_NHPEADZ_1401C7250.cpp" />
    <ClCompile Include="source\LogInWebAgentServerCNetworkEXAEAA_NHPEADZ_1401DA860.cpp" />
    <ClCompile Include="source\login_cancel_auto_tradeCMgrAvatorItemHistoryQEAAXH_140239D60.cpp" />
    <ClCompile Include="source\lower_bound_HashV_Hmap_traitsHPEAVCAsyncLogInfoVha_1403C30D0.cpp" />
    <ClCompile Include="source\make_pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdYAAUp_1403C75D0.cpp" />
    <ClCompile Include="source\max_sizeallocatorUpairCBHPEAVCAsyncLogInfostdstdQE_1403C6F00.cpp" />
    <ClCompile Include="source\max_sizeallocatorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C7200.cpp" />
    <ClCompile Include="source\max_sizelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C5F60.cpp" />
    <ClCompile Include="source\max_sizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C69A0.cpp" />
    <ClCompile Include="source\NotifyLogInSetBuffCRaceBuffInfoByHolyQuestAEAAXGZ_1403B42D0.cpp" />
    <ClCompile Include="source\OnCheckSession_FirstVerifyCHackShieldExSystemUEAA__140417250.cpp" />
    <ClCompile Include="source\OnCheckSession_FirstVerifyCNationSettingManagerQEA_140229470.cpp" />
    <ClCompile Include="source\OnCheckSession_FirstVerifyHACKSHEILD_PARAM_ANTICPU_140417960.cpp" />
    <ClCompile Include="source\OnConnectSessionCHackShieldExSystemUEAAXHZ_1404170D0.cpp" />
    <ClCompile Include="source\OnConnectSessionCNationSettingManagerQEAAXHZ_140229400.cpp" />
    <ClCompile Include="source\OnDisConnectSessionCHackShieldExSystemUEAAXHZ_140417140.cpp" />
    <ClCompile Include="source\OnDisConnectSessionCNationSettingManagerQEAAXHZ_1402294F0.cpp" />
    <ClCompile Include="source\OnLoopSessionCHackShieldExSystemUEAAXHZ_1404171A0.cpp" />
    <ClCompile Include="source\OnRecvSessionHACKSHEILD_PARAM_ANTICPUEAA_NPEAVCHac_140417F10.cpp" />
    <ClCompile Include="source\OnRecvSession_ClientCheckSum_ResponseHACKSHEILD_PA_140418120.cpp" />
    <ClCompile Include="source\OnRecvSession_ClientCrc_ResponseHACKSHEILD_PARAM_A_140418290.cpp" />
    <ClCompile Include="source\OnRecvSession_ServerCheckSum_RequestHACKSHEILD_PAR_140417FB0.cpp" />
    <ClCompile Include="source\R3InvalidateDeviceYAJXZ_1404E9FC0.cpp" />
    <ClCompile Include="source\resizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C3F20.cpp" />
    <ClCompile Include="source\SendMsg_CurAllUserLoginCBillingIEAAXXZ_14028D610.cpp" />
    <ClCompile Include="source\SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_1402570F0.cpp" />
    <ClCompile Include="source\SendMsg_LoginCBillingIDMEAA_NPEAD00FPEAU_SYSTEMTIM_14028E600.cpp" />
    <ClCompile Include="source\SendMsg_LoginCBillingJPMEAA_NPEAD00FPEAU_SYSTEMTIM_14028ECC0.cpp" />
    <ClCompile Include="source\SendMsg_LoginCBillingMEAA_NPEAD00FPEAU_SYSTEMTIMEJ_14028D3C0.cpp" />
    <ClCompile Include="source\SendMsg_LoginCBillingNULLMEAA_NPEAD00FPEAU_SYSTEMT_14028DC10.cpp" />
    <ClCompile Include="source\SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAXHZ_1402D8540.cpp" />
    <ClCompile Include="source\SetOccDialogInfoCDialogMEAAHPEAU_AFX_OCC_DIALOG_IN_1404DBD42.cpp" />
    <ClCompile Include="source\SetOccDialogInfoCFormViewMEAAHPEAU_AFX_OCC_DIALOG__1404DC156.cpp" />
    <ClCompile Include="source\SetOccDialogInfoCWndMEAAHPEAU_AFX_OCC_DIALOG_INFOZ_1404DBE1A.cpp" />
    <ClCompile Include="source\Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_1400A6BA0.cpp" />
    <ClCompile Include="source\Set_AuthKeyTicketMiningTicketQEAAXIZ_140078ED0.cpp" />
    <ClCompile Include="source\sizelistUpairCBHPEAVCAsyncLogInfostdVallocatorUpai_1403C4650.cpp" />
    <ClCompile Include="source\sizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C4210.cpp" />
    <ClCompile Include="source\size_apex_send_loginQEAAHXZ_140410BF0.cpp" />
    <ClCompile Include="source\size_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_comp_1403C3080.cpp" />
    <ClCompile Include="source\unchecked_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyn_1403C7970.cpp" />
    <ClCompile Include="source\unchecked_uninitialized_copyPEAV_Iterator0AlistUpa_1403C8D20.cpp" />
    <ClCompile Include="source\unchecked_uninitialized_fill_nPEAV_Iterator0AlistU_1403C7C50.cpp" />
    <ClCompile Include="source\UpdateLogFileNameCAsyncLogInfoQEAAXXZ_1403BD0F0.cpp" />
    <ClCompile Include="source\UpdateLogInCompleteCUnmannedTraderControllerQEAAEP_14034E440.cpp" />
    <ClCompile Include="source\Update_TrunkPasswordCUserDBQEAA_NPEADZ_140116FD0.cpp" />
    <ClCompile Include="source\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAD0.cpp" />
    <ClCompile Include="source\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAF0.cpp" />
    <ClCompile Include="source\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADD90.cpp" />
    <ClCompile Include="source\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A900.cpp" />
    <ClCompile Include="source\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A920.cpp" />
    <ClCompile Include="source\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046AD80.cpp" />
    <ClCompile Include="source\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_140551AC0.cpp" />
    <ClCompile Include="source\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_1405AD4F0.cpp" />
    <ClCompile Include="source\ValidateDL_PrivateKeyImplVDL_GroupParameters_DSACr_140568460.cpp" />
    <ClCompile Include="source\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_1404515F0.cpp" />
    <ClCompile Include="source\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_140558CA0.cpp" />
    <ClCompile Include="source\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFPCr_140635EE0.cpp" />
    <ClCompile Include="source\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFP_D_140637C30.cpp" />
    <ClCompile Include="source\ValidateDL_PublicKeyImplVDL_GroupParameters_DSACry_140568F30.cpp" />
    <ClCompile Include="source\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVEC2_140558420.cpp" />
    <ClCompile Include="source\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVECP_140450870.cpp" />
    <ClCompile Include="source\ValidateDL_PublicKeyImplVDL_GroupParameters_GFPCry_1406369F0.cpp" />
    <ClCompile Include="source\ValidateDL_PublicKeyImplVDL_GroupParameters_GFP_De_1406373A0.cpp" />
    <ClCompile Include="source\ValidateElementDL_GroupParameters_ECVEC2NCryptoPPC_140583CF0.cpp" />
    <ClCompile Include="source\ValidateElementDL_GroupParameters_ECVECPCryptoPPCr_14057FB10.cpp" />
    <ClCompile Include="source\ValidateElementDL_GroupParameters_IntegerBasedCryp_140630AE0.cpp" />
    <ClCompile Include="source\ValidateGroupDL_GroupParameters_DSACryptoPPUEBA_NA_140630230.cpp" />
    <ClCompile Include="source\ValidateGroupDL_GroupParameters_ECVEC2NCryptoPPCry_1405835A0.cpp" />
    <ClCompile Include="source\ValidateGroupDL_GroupParameters_ECVECPCryptoPPCryp_14057F300.cpp" />
    <ClCompile Include="source\ValidateGroupDL_GroupParameters_IntegerBasedCrypto_140630680.cpp" />
    <ClCompile Include="source\ValidateParametersEC2NCryptoPPQEBA_NAEAVRandomNumb_14062E210.cpp" />
    <ClCompile Include="source\ValidateParametersECPCryptoPPQEBA_NAEAVRandomNumbe_14060E2A0.cpp" />
    <ClCompile Include="source\validatetable_objlua_tinkerQEAA_NXZ_1404462F0.cpp" />
    <ClCompile Include="source\Y_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7420.cpp" />
    <ClCompile Include="source\Y_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6DD0.cpp" />
    <ClCompile Include="source\_AllocateU_Node_List_nodUpairCBHPEAVCAsyncLogInfos_1403C7E90.cpp" />
    <ClCompile Include="source\_AllocateV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7D00.cpp" />
    <ClCompile Include="source\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6370.cpp" />
    <ClCompile Include="source\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6500.cpp" />
    <ClCompile Include="source\_BuyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C7100.cpp" />
    <ClCompile Include="source\_CAsyncLogInfoInit__1_dtor0_1403BD0C0.cpp" />
    <ClCompile Include="source\_CAsyncLogInfo_CAsyncLogInfo__1_dtor0_1403BCB50.cpp" />
    <ClCompile Include="source\_CEnglandBillingMgrCallFunc_RFOnline_Auth__1_dtor0_140319C50.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLogIn__1_dtor0_1403A5C90.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLogIn__1_dtor1_1403A5CC0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLogIn__1_dtor2_1403A5CF0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLogIn__1_dtor3_1403A5D20.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLogIn__1_dtor0_1403AD090.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLogIn__1_dtor1_1403AD0C0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLogIn__1_dtor2_1403AD0F0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLogIn__1_dtor3_1403AD120.cpp" />
    <ClCompile Include="source\_ConstructPEAU_Node_List_nodUpairCBHPEAVCAsyncLogI_1403C7F50.cpp" />
    <ClCompile Include="source\_ConstructUpairCBHPEAVCAsyncLogInfostdU12stdYAXPEA_1403C7DB0.cpp" />
    <ClCompile Include="source\_ConstructV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C8B70.cpp" />
    <ClCompile Include="source\_Construct_nvectorV_Iterator0AlistUpairCBHPEAVCAsy_1403C6820.cpp" />
    <ClCompile Include="source\_Copy_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8AD0.cpp" />
    <ClCompile Include="source\_Copy_optPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8530.cpp" />
    <ClCompile Include="source\_CryptoPPDL_PrivateKeyImpl_CryptoPPDL_GroupParamet_140451850.cpp" />
    <ClCompile Include="source\_DestroyPEAU_Node_List_nodUpairCBHPEAVCAsyncLogInf_1403C7BC0.cpp" />
    <ClCompile Include="source\_DestroyU_Node_List_nodUpairCBHPEAVCAsyncLogInfost_1403C7F40.cpp" />
    <ClCompile Include="source\_DestroyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C69F0.cpp" />
    <ClCompile Include="source\_DestroyV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8C60.cpp" />
    <ClCompile Include="source\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C7BD0.cpp" />
    <ClCompile Include="source\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C8810.cpp" />
    <ClCompile Include="source\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_140465900.cpp" />
    <ClCompile Include="source\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_14046AC80.cpp" />
    <ClCompile Include="source\_FillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8680.cpp" />
    <ClCompile Include="source\_GCAsyncLogInfoQEAAPEAXIZ_1403C14F0.cpp" />
    <ClCompile Include="source\_Get_iter_from_vec_HashV_Hmap_traitsHPEAVCAsyncLog_1403C2E50.cpp" />
    <ClCompile Include="source\_G_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVallo_1403C8CB0.cpp" />
    <ClCompile Include="source\_Hashval_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash__1403C3530.cpp" />
    <ClCompile Include="source\_IncsizelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C4D90.cpp" />
    <ClCompile Include="source\_InsertlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C4670.cpp" />
    <ClCompile Include="source\_InsertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C80C0.cpp" />
    <ClCompile Include="source\_Insert_nvectorV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C5320.cpp" />
    <ClCompile Include="source\_Iter_catV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C8060.cpp" />
    <ClCompile Include="source\_Iter_randomPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8470.cpp" />
    <ClCompile Include="source\_Kfn_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUl_1403C35E0.cpp" />
    <ClCompile Include="source\_Move_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8760.cpp" />
    <ClCompile Include="source\_Move_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8700.cpp" />
    <ClCompile Include="source\_Mynode_Const_iterator0AlistUpairCBHPEAVCAsyncLogI_1403C5C50.cpp" />
    <ClCompile Include="source\_MyvallistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C3610.cpp" />
    <ClCompile Include="source\_NextnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C3600.cpp" />
    <ClCompile Include="source\_PrevnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C4590.cpp" />
    <ClCompile Include="source\_Ptr_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C84D0.cpp" />
    <ClCompile Include="source\_SplicelistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C38D0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C18C0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2480.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24B0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24E0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2510.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2540.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2570.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25A0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25D0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2600.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2630.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2660.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2690.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C26C0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2700.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FA0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FD0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3000.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3370.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33A0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33D0.cpp" />
    <ClCompile Include="source\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3410.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3820.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3850.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3C90.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CC0.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CF0.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D20.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D50.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D80.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4730.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4A70.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AA0.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AD0.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B10.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B50.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B90.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4E80.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6130.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6160.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6190.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6440.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6600.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7310.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7340.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7800.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7830.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7860.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7890.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C78C0.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8200.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8230.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8260.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8290.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C82C0.cpp" />
    <ClCompile Include="source\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8390.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C40F0.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4120.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4150.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5150.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5180.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C51B0.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5860.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5890.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C58C0.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5920.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C68B0.cpp" />
    <ClCompile Include="source\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C6B80.cpp" />
    <ClCompile Include="source\_std_Construct_stdlist_stdpair_int_const__CAsyncLo_1403C8C00.cpp" />
    <ClCompile Include="source\_std_Uninit_copy_stdlist_stdpair_int_const__CAsync_1403C8E60.cpp" />
    <ClCompile Include="source\_std_Uninit_fill_n_stdlist_stdpair_int_const__CAsy_1403C8920.cpp" />
    <ClCompile Include="source\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D10.cpp" />
    <ClCompile Include="source\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D40.cpp" />
    <ClCompile Include="source\_TidylistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C4CC0.cpp" />
    <ClCompile Include="source\_TidyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C5240.cpp" />
    <ClCompile Include="source\_UfillvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6A60.cpp" />
    <ClCompile Include="source\_UmovePEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7A30.cpp" />
    <ClCompile Include="source\_Unchecked_move_backwardPEAV_Iterator0AlistUpairCB_1403C7B00.cpp" />
    <ClCompile Include="source\_Unchecked_uninitialized_movePEAV_Iterator0AlistUp_1403C85D0.cpp" />
    <ClCompile Include="source\_Uninit_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8DD0.cpp" />
    <ClCompile Include="source\_Uninit_fill_nPEAV_Iterator0AlistUpairCBHPEAVCAsyn_1403C8890.cpp" />
    <ClCompile Include="source\_Uninit_movePEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8A60.cpp" />
    <ClCompile Include="source\_ValidateImageBase_1404DE4C0.cpp" />
    <ClCompile Include="source\_XlenvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C6AF0.cpp" />
  </ItemGroup>
  
  <ItemGroup>
    <ClInclude Include="headers\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAAEBV0_1403C5F40.h" />
    <ClInclude Include="headers\0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAXZ_1403C35F0.h" />
    <ClInclude Include="headers\0allocatorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C6C50.h" />
    <ClInclude Include="headers\0CAsyncLogInfoQEAAXZ_1403BC9F0.h" />
    <ClInclude Include="headers\0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQEAA_1403C7590.h" />
    <ClInclude Include="headers\0hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C17E0.h" />
    <ClInclude Include="headers\0listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C45B0.h" />
    <ClInclude Include="headers\0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.h" />
    <ClInclude Include="headers\0pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C43F0.h" />
    <ClInclude Include="headers\0pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdQEAAAEBW4_1403C8010.h" />
    <ClInclude Include="headers\0UpairCBHPEAVCAsyncLogInfostdallocatorPEAU_Node_Li_1403C7E70.h" />
    <ClInclude Include="headers\0UpairCBHPEAVCAsyncLogInfostdallocatorU_Node_List__1403C7FF0.h" />
    <ClInclude Include="headers\0UpairCBHPEAVCAsyncLogInfostdallocatorV_Iterator0A_1403C7670.h" />
    <ClInclude Include="headers\0vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4EF0.h" />
    <ClInclude Include="headers\0W4ASYNC_LOG_TYPEPEAVCAsyncLogInfopairCBHPEAVCAsyn_1403C7630.h" />
    <ClInclude Include="headers\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1560.h" />
    <ClInclude Include="headers\0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C5E20.h" />
    <ClInclude Include="headers\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C1480.h" />
    <ClInclude Include="headers\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5B90.h" />
    <ClInclude Include="headers\0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C6D10.h" />
    <ClInclude Include="headers\0_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C2EC0.h" />
    <ClInclude Include="headers\0_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUless_1403C4520.h" />
    <ClInclude Include="headers\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2D20.h" />
    <ClInclude Include="headers\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C42C0.h" />
    <ClInclude Include="headers\0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C5B30.h" />
    <ClInclude Include="headers\0_List_nodUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C7520.h" />
    <ClInclude Include="headers\0_List_ptrUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C6F70.h" />
    <ClInclude Include="headers\0_List_valUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C66F0.h" />
    <ClInclude Include="headers\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C5EE0.h" />
    <ClInclude Include="headers\0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C74D0.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C5E70.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C73C0.h" />
    <ClInclude Include="headers\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5DC0.h" />
    <ClInclude Include="headers\0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6D70.h" />
    <ClInclude Include="headers\0_Vector_valV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6BE0.h" />
    <ClInclude Include="headers\1CAsyncLogInfoQEAAXZ_1403BCA80.h" />
    <ClInclude Include="headers\1hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHstd_1403C1170.h" />
    <ClInclude Include="headers\1listUpairCBHPEAVCAsyncLogInfostdVallocatorUpairCB_1403C3630.h" />
    <ClInclude Include="headers\1MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140464F50.h" />
    <ClInclude Include="headers\1pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdV_1403C1670.h" />
    <ClInclude Include="headers\1vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C3EE0.h" />
    <ClInclude Include="headers\1_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C1230.h" />
    <ClInclude Include="headers\1_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C11F0.h" />
    <ClInclude Include="headers\1_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compare_1403C1860.h" />
    <ClInclude Include="headers\1_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C11B0.h" />
    <ClInclude Include="headers\1_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C44E0.h" />
    <ClInclude Include="headers\1_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C44A0.h" />
    <ClInclude Include="headers\1_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C4460.h" />
    <ClInclude Include="headers\4_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU12s_1403C2DF0.h" />
    <ClInclude Include="headers\4_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2D80.h" />
    <ClInclude Include="headers\4_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2CC0.h" />
    <ClInclude Include="headers\8UpairCBHPEAVCAsyncLogInfostdU01stdYA_NAEBVallocat_1403C7690.h" />
    <ClInclude Include="headers\8_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400A6CA0.h" />
    <ClInclude Include="headers\8_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2BE0.h" />
    <ClInclude Include="headers\8_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7460.h" />
    <ClInclude Include="headers\9_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400CFE90.h" />
    <ClInclude Include="headers\9_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2C50.h" />
    <ClInclude Include="headers\9_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C6E30.h" />
    <ClInclude Include="headers\AccountServerLoginCMainThreadQEAAXXZ_1401F8140.h" />
    <ClInclude Include="headers\allocateallocatorU_Node_List_nodUpairCBHPEAVCAsync_1403C7000.h" />
    <ClInclude Include="headers\allocateallocatorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6CC0.h" />
    <ClInclude Include="headers\AuthLastCriTicketMiningTicketQEAAHGEEEEZ_1400D01D0.h" />
    <ClInclude Include="headers\AuthLastMentalTicketMiningTicketQEAAHGEEEEZ_1400CFDB0.h" />
    <ClInclude Include="headers\AuthMiningTicketCHolyStoneSystemQEAA_NIZ_14027DBD0.h" />
    <ClInclude Include="headers\auto_trade_login_sellCMgrAvatorItemHistoryQEAAXPEB_14023A3E0.h" />
    <ClInclude Include="headers\AvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C4290.h" />
    <ClInclude Include="headers\beginlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C3670.h" />
    <ClInclude Include="headers\beginvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C4F90.h" />
    <ClInclude Include="headers\begin_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_com_1403C1910.h" />
    <ClInclude Include="headers\CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEAU__1403198F0.h" />
    <ClInclude Include="headers\CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU_pa_1403213A0.h" />
    <ClInclude Include="headers\CallProc_RFOnlineAuthCRFCashItemDatabaseQEAAHAEAU__140482430.h" />
    <ClInclude Include="headers\CallProc_RFOnlineAuth_JapCRFCashItemDatabaseQEAAHA_1404836A0.h" />
    <ClInclude Include="headers\capacityvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C6920.h" />
    <ClInclude Include="headers\clearlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C6250.h" />
    <ClInclude Include="headers\CN_InvalidateNatureYAXXZ_140504ED0.h" />
    <ClInclude Include="headers\CompleteLogInCompeteCUnmannedTraderControllerQEAAX_14034EF80.h" />
    <ClInclude Include="headers\constructallocatorPEAU_Node_List_nodUpairCBHPEAVCA_1403C70A0.h" />
    <ClInclude Include="headers\constructallocatorUpairCBHPEAVCAsyncLogInfostdstdQ_1403C6EA0.h" />
    <ClInclude Include="headers\constructallocatorV_Iterator0AlistUpairCBHPEAVCAsy_1403C89B0.h" />
    <ClInclude Include="headers\C_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C2AF0.h" />
    <ClInclude Include="headers\D3D_R3InvalidateDeviceYAJXZ_14050B040.h" />
    <ClInclude Include="headers\deallocateallocatorU_Node_List_nodUpairCBHPEAVCAsy_1403C6780.h" />
    <ClInclude Include="headers\deallocateallocatorV_Iterator0AlistUpairCBHPEAVCAs_1403C6C70.h" />
    <ClInclude Include="headers\destroyallocatorPEAU_Node_List_nodUpairCBHPEAVCAsy_1403C67D0.h" />
    <ClInclude Include="headers\destroyallocatorU_Node_List_nodUpairCBHPEAVCAsyncL_1403C7050.h" />
    <ClInclude Include="headers\destroyallocatorV_Iterator0AlistUpairCBHPEAVCAsync_1403C8A10.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_0_14057B120.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_10_14057B3A0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_11_14057B3E0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_12_14057B420.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_13_14057B460.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_14057B0E0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_14_14057C3D0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_15_14057C410.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_16_14057C450.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_17_14057C490.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_18_14057C4D0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_19_14057C510.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_1_14057B160.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_20_14057C550.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_21_14057C590.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_22_14057C5D0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_23_14057C610.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_24_14057C650.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_25_14057C690.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_26_14057C6D0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_27_14057C710.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_28_14057C750.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_29_14057C790.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_2_14057B1A0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_30_14057C7D0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_31_14057C810.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_3_14057B1E0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_4_14057B220.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_5_14057B260.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_6_14057B2A0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_7_14057B2E0.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_8_14057B320.h" />
    <ClInclude Include="headers\dtor00__F_afxSessionMapYAXXZ4HA_9_14057B360.h" />
    <ClInclude Include="headers\D_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B30.h" />
    <ClInclude Include="headers\D_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4310.h" />
    <ClInclude Include="headers\endlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpair_1403C36F0.h" />
    <ClInclude Include="headers\endvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C5000.h" />
    <ClInclude Include="headers\end_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compa_1403C1990.h" />
    <ClInclude Include="headers\eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C4790.h" />
    <ClInclude Include="headers\eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C5FB0.h" />
    <ClInclude Include="headers\erasevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C5070.h" />
    <ClInclude Include="headers\E_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C2B80.h" />
    <ClInclude Include="headers\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C4350.h" />
    <ClInclude Include="headers\E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C7270.h" />
    <ClInclude Include="headers\fillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C7AA0.h" />
    <ClInclude Include="headers\find_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_comp_1403C2A70.h" />
    <ClInclude Include="headers\F_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C5BF0.h" />
    <ClInclude Include="headers\F_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C43A0.h" />
    <ClInclude Include="headers\GenerateEphemeralKeyPairAuthenticatedKeyAgreementD_1405F6600.h" />
    <ClInclude Include="headers\GenerateStaticKeyPairAuthenticatedKeyAgreementDoma_1405F65A0.h" />
    <ClInclude Include="headers\GetCountCAsyncLogInfoQEAAKXZ_1403C16B0.h" />
    <ClInclude Include="headers\GetDirPathCAsyncLogInfoQEAAPEBDXZ_1403C1630.h" />
    <ClInclude Include="headers\GetFileNameCAsyncLogInfoQEAAPEBDXZ_1403C16D0.h" />
    <ClInclude Include="headers\GetOccDialogInfoCDialogMEAAPEAU_AFX_OCC_DIALOG_INF_1404DBD48.h" />
    <ClInclude Include="headers\GetOccDialogInfoCFormViewMEAAPEAU_AFX_OCC_DIALOG_I_1404DC15C.h" />
    <ClInclude Include="headers\GetOccDialogInfoCWndMEAAPEAU_AFX_OCC_DIALOG_INFOXZ_1404DBE20.h" />
    <ClInclude Include="headers\GetTypeNameCAsyncLogInfoQEAAPEBDXZ_1403C1650.h" />
    <ClInclude Include="headers\H_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5C70.h" />
    <ClInclude Include="headers\IncreaseCountCAsyncLogInfoQEAAXXZ_1403C16F0.h" />
    <ClInclude Include="headers\InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.h" />
    <ClInclude Include="headers\Init_AuthKeyTicketMiningTicketQEAAXXZ_140073BC0.h" />
    <ClInclude Include="headers\insertlistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C3760.h" />
    <ClInclude Include="headers\insertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfostd_1403C76B0.h" />
    <ClInclude Include="headers\insert_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_co_1403C1A10.h" />
    <ClInclude Include="headers\InvalidateDeviceObjectsCR3FontQEAAJXZ_140528820.h" />
    <ClInclude Include="headers\InvalidateSkySkyQEAAXXZ_1405229B0.h" />
    <ClInclude Include="headers\InvalidateSunSunQEAAXXZ_1405221E0.h" />
    <ClInclude Include="headers\IsLogInStateCUnmannedTraderUserInfoQEAA_NXZ_140366F20.h" />
    <ClInclude Include="headers\j_0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAAEB_14000EF34.h" />
    <ClInclude Include="headers\j_0allocatorUpairCBHPEAVCAsyncLogInfostdstdQEAAXZ_14000C0BD.h" />
    <ClInclude Include="headers\j_0allocatorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_140012E9A.h" />
    <ClInclude Include="headers\j_0CAsyncLogInfoQEAAXZ_14000E4F8.h" />
    <ClInclude Include="headers\j_0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQE_140013B1F.h" />
    <ClInclude Include="headers\j_0hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHs_14000DFBC.h" />
    <ClInclude Include="headers\j_0listUpairCBHPEAVCAsyncLogInfostdVallocatorUpair_140011635.h" />
    <ClInclude Include="headers\j_0MessageAuthenticationCodeImplVHMAC_BaseCryptoPP_14000FCA4.h" />
    <ClInclude Include="headers\j_0pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1400064BA.h" />
    <ClInclude Include="headers\j_0pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdQEAAAEB_140007469.h" />
    <ClInclude Include="headers\j_0UpairCBHPEAVCAsyncLogInfostdallocatorPEAU_Node__140004836.h" />
    <ClInclude Include="headers\j_0UpairCBHPEAVCAsyncLogInfostdallocatorU_Node_Lis_140013516.h" />
    <ClInclude Include="headers\j_0UpairCBHPEAVCAsyncLogInfostdallocatorV_Iterator_140002CB1.h" />
    <ClInclude Include="headers\j_0vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140002C1B.h" />
    <ClInclude Include="headers\j_0W4ASYNC_LOG_TYPEPEAVCAsyncLogInfopairCBHPEAVCAs_14000D4F4.h" />
    <ClInclude Include="headers\j_0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_140005678.h" />
    <ClInclude Include="headers\j_0_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_14000FC2C.h" />
    <ClInclude Include="headers\j_0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14000C6B2.h" />
    <ClInclude Include="headers\j_0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140010E06.h" />
    <ClInclude Include="headers\j_0_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14001213E.h" />
    <ClInclude Include="headers\j_0_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compa_14001253F.h" />
    <ClInclude Include="headers\j_0_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUle_14000B91A.h" />
    <ClInclude Include="headers\j_0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000269E.h" />
    <ClInclude Include="headers\j_0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000CA54.h" />
    <ClInclude Include="headers\j_0_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140010D1B.h" />
    <ClInclude Include="headers\j_0_List_nodUpairCBHPEAVCAsyncLogInfostdVallocator_140002DC4.h" />
    <ClInclude Include="headers\j_0_List_ptrUpairCBHPEAVCAsyncLogInfostdVallocator_1400071CB.h" />
    <ClInclude Include="headers\j_0_List_valUpairCBHPEAVCAsyncLogInfostdVallocator_1400024CD.h" />
    <ClInclude Include="headers\j_0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_14000BAE1.h" />
    <ClInclude Include="headers\j_0_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140011847.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140003EC2.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorV_Iterator0AlistUpairCBHP_14000D526.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_140007405.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_14000A8FD.h" />
    <ClInclude Include="headers\j_0_Vector_valV_Iterator0AlistUpairCBHPEAVCAsyncLo_1400107E4.h" />
    <ClInclude Include="headers\j_1CAsyncLogInfoQEAAXZ_14000F182.h" />
    <ClInclude Include="headers\j_1hash_mapHPEAVCAsyncLogInfoVhash_compareHUlessHs_14000900C.h" />
    <ClInclude Include="headers\j_1listUpairCBHPEAVCAsyncLogInfostdVallocatorUpair_140011950.h" />
    <ClInclude Include="headers\j_1MessageAuthenticationCodeImplVHMAC_BaseCryptoPP_1400097F5.h" />
    <ClInclude Include="headers\j_1pairV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_140005970.h" />
    <ClInclude Include="headers\j_1vectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140009B47.h" />
    <ClInclude Include="headers\j_1_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_14000684D.h" />
    <ClInclude Include="headers\j_1_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_1400047BE.h" />
    <ClInclude Include="headers\j_1_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_compa_14000839B.h" />
    <ClInclude Include="headers\j_1_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000788D.h" />
    <ClInclude Include="headers\j_1_RanitV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140006F73.h" />
    <ClInclude Include="headers\j_1_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140008B52.h" />
    <ClInclude Include="headers\j_1_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_140012580.h" />
    <ClInclude Include="headers\j_4_BiditUpairCBHPEAVCAsyncLogInfostd_JPEBU12AEBU1_140009D6D.h" />
    <ClInclude Include="headers\j_4_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140001488.h" />
    <ClInclude Include="headers\j_4_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140005547.h" />
    <ClInclude Include="headers\j_8UpairCBHPEAVCAsyncLogInfostdU01stdYA_NAEBValloc_14000BDD9.h" />
    <ClInclude Include="headers\j_8_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_140010A91.h" />
    <ClInclude Include="headers\j_8_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140012F2B.h" />
    <ClInclude Include="headers\j_8_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140012F9E.h" />
    <ClInclude Include="headers\j_9_AuthKeyTicketMiningTicketQEBA_NAEBU01Z_1400090B1.h" />
    <ClInclude Include="headers\j_9_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14000C310.h" />
    <ClInclude Include="headers\j_9_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140006938.h" />
    <ClInclude Include="headers\j_AccountServerLoginCMainThreadQEAAXXZ_14001102C.h" />
    <ClInclude Include="headers\j_allocateallocatorU_Node_List_nodUpairCBHPEAVCAsy_140001794.h" />
    <ClInclude Include="headers\j_allocateallocatorV_Iterator0AlistUpairCBHPEAVCAs_140001E7E.h" />
    <ClInclude Include="headers\j_AuthLastCriTicketMiningTicketQEAAHGEEEEZ_14000DDAA.h" />
    <ClInclude Include="headers\j_AuthLastMentalTicketMiningTicketQEAAHGEEEEZ_14000F38A.h" />
    <ClInclude Include="headers\j_AuthMiningTicketCHolyStoneSystemQEAA_NIZ_140009EBC.h" />
    <ClInclude Include="headers\j_auto_trade_login_sellCMgrAvatorItemHistoryQEAAXP_140006FAA.h" />
    <ClInclude Include="headers\j_AvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140002306.h" />
    <ClInclude Include="headers\j_beginlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1400096E2.h" />
    <ClInclude Include="headers\j_beginvectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_14000CFE0.h" />
    <ClInclude Include="headers\j_begin_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_c_1400049AD.h" />
    <ClInclude Include="headers\j_CallFunc_RFOnline_AuthCEnglandBillingMgrQEAAHAEA_14000D4B3.h" />
    <ClInclude Include="headers\j_CallFunc_RFOnline_AuthCRusiaBillingMgrQEAAHAEAU__140001E24.h" />
    <ClInclude Include="headers\j_CallProc_RFOnlineAuthCRFCashItemDatabaseQEAAHAEA_14000C2F2.h" />
    <ClInclude Include="headers\j_CallProc_RFOnlineAuth_JapCRFCashItemDatabaseQEAA_14000B334.h" />
    <ClInclude Include="headers\j_capacityvectorV_Iterator0AlistUpairCBHPEAVCAsync_14000CB58.h" />
    <ClInclude Include="headers\j_clearlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_140008D82.h" />
    <ClInclude Include="headers\j_CompleteLogInCompeteCUnmannedTraderControllerQEA_14000E66A.h" />
    <ClInclude Include="headers\j_constructallocatorPEAU_Node_List_nodUpairCBHPEAV_14000EF6B.h" />
    <ClInclude Include="headers\j_constructallocatorUpairCBHPEAVCAsyncLogInfostdst_14000ECC8.h" />
    <ClInclude Include="headers\j_constructallocatorV_Iterator0AlistUpairCBHPEAVCA_140006CB2.h" />
    <ClInclude Include="headers\j_C_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140009BB0.h" />
    <ClInclude Include="headers\j_deallocateallocatorU_Node_List_nodUpairCBHPEAVCA_140010910.h" />
    <ClInclude Include="headers\j_deallocateallocatorV_Iterator0AlistUpairCBHPEAVC_14000176C.h" />
    <ClInclude Include="headers\j_destroyallocatorPEAU_Node_List_nodUpairCBHPEAVCA_140001C6C.h" />
    <ClInclude Include="headers\j_destroyallocatorU_Node_List_nodUpairCBHPEAVCAsyn_14000A6EB.h" />
    <ClInclude Include="headers\j_destroyallocatorV_Iterator0AlistUpairCBHPEAVCAsy_140013A98.h" />
    <ClInclude Include="headers\j_D_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140003797.h" />
    <ClInclude Include="headers\j_D_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140002C70.h" />
    <ClInclude Include="headers\j_endlistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_140008A76.h" />
    <ClInclude Include="headers\j_endvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_140004A57.h" />
    <ClInclude Include="headers\j_end_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_com_1400029EB.h" />
    <ClInclude Include="headers\j_eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorU_140003D1E.h" />
    <ClInclude Include="headers\j_eraselistUpairCBHPEAVCAsyncLogInfostdVallocatorU_14000C559.h" />
    <ClInclude Include="headers\j_erasevectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_140010C7B.h" />
    <ClInclude Include="headers\j_E_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_140003A80.h" />
    <ClInclude Include="headers\j_E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140007F54.h" />
    <ClInclude Include="headers\j_E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_14000B758.h" />
    <ClInclude Include="headers\j_fillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140010B90.h" />
    <ClInclude Include="headers\j_find_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_co_14000F4A2.h" />
    <ClInclude Include="headers\j_F_Const_iterator0AlistUpairCBHPEAVCAsyncLogInfos_14000A187.h" />
    <ClInclude Include="headers\j_F_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVall_140011E87.h" />
    <ClInclude Include="headers\j_GetCountCAsyncLogInfoQEAAKXZ_14000B60E.h" />
    <ClInclude Include="headers\j_GetDirPathCAsyncLogInfoQEAAPEBDXZ_140011A77.h" />
    <ClInclude Include="headers\j_GetFileNameCAsyncLogInfoQEAAPEBDXZ_1400112A7.h" />
    <ClInclude Include="headers\j_GetTypeNameCAsyncLogInfoQEAAPEBDXZ_140003337.h" />
    <ClInclude Include="headers\j_H_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_14000B6F4.h" />
    <ClInclude Include="headers\j_IncreaseCountCAsyncLogInfoQEAAXXZ_140007B5D.h" />
    <ClInclude Include="headers\j_InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKA_14000C3F1.h" />
    <ClInclude Include="headers\j_Init_AuthKeyTicketMiningTicketQEAAXXZ_140004B5B.h" />
    <ClInclude Include="headers\j_insertlistUpairCBHPEAVCAsyncLogInfostdVallocator_14000EC2D.h" />
    <ClInclude Include="headers\j_insertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1400133BD.h" />
    <ClInclude Include="headers\j_insert_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash__140008F53.h" />
    <ClInclude Include="headers\j_IsLogInStateCUnmannedTraderUserInfoQEAA_NXZ_140009F3E.h" />
    <ClInclude Include="headers\j_LoginCBillingIDUEAAXPEAVCUserDBZ_140002B76.h" />
    <ClInclude Include="headers\j_LoginCBillingJPUEAAXPEAVCUserDBZ_140003FDA.h" />
    <ClInclude Include="headers\j_LoginCBillingManagerQEAAXPEAVCUserDBZ_140011F54.h" />
    <ClInclude Include="headers\j_LoginCBillingNULLUEAAXPEAVCUserDBZ_140006D4D.h" />
    <ClInclude Include="headers\j_LoginCBillingUEAAXPEAVCUserDBZ_14000AFD8.h" />
    <ClInclude Include="headers\j_LogInCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKE_1400127B5.h" />
    <ClInclude Include="headers\j_LoginCNormalGuildBattleGuildMemberGUILD_BATTLEQE_14000E304.h" />
    <ClInclude Include="headers\j_LogInCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1400062BC.h" />
    <ClInclude Include="headers\j_LogInCNormalGuildBattleManagerGUILD_BATTLEQEAAXH_14001391C.h" />
    <ClInclude Include="headers\j_LogInControllServerCNetworkEXAEAA_NHPEADZ_14000E8DB.h" />
    <ClInclude Include="headers\j_LogInWebAgentServerCNetworkEXAEAA_NHPEADZ_1400026FD.h" />
    <ClInclude Include="headers\j_login_cancel_auto_tradeCMgrAvatorItemHistoryQEAA_140011B71.h" />
    <ClInclude Include="headers\j_lower_bound_HashV_Hmap_traitsHPEAVCAsyncLogInfoV_1400014EC.h" />
    <ClInclude Include="headers\j_make_pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdYAA_1400120E4.h" />
    <ClInclude Include="headers\j_max_sizeallocatorUpairCBHPEAVCAsyncLogInfostdstd_140006AAA.h" />
    <ClInclude Include="headers\j_max_sizeallocatorV_Iterator0AlistUpairCBHPEAVCAs_1400094B2.h" />
    <ClInclude Include="headers\j_max_sizelistUpairCBHPEAVCAsyncLogInfostdVallocat_140004098.h" />
    <ClInclude Include="headers\j_max_sizevectorV_Iterator0AlistUpairCBHPEAVCAsync_14000E9C6.h" />
    <ClInclude Include="headers\j_NotifyLogInSetBuffCRaceBuffInfoByHolyQuestAEAAXG_14000A1A5.h" />
    <ClInclude Include="headers\j_OnCheckSession_FirstVerifyCHackShieldExSystemUEA_1400114FA.h" />
    <ClInclude Include="headers\j_OnCheckSession_FirstVerifyCNationSettingManagerQ_140008558.h" />
    <ClInclude Include="headers\j_OnCheckSession_FirstVerifyHACKSHEILD_PARAM_ANTIC_140007789.h" />
    <ClInclude Include="headers\j_OnConnectSessionCHackShieldExSystemUEAAXHZ_14000FEA7.h" />
    <ClInclude Include="headers\j_OnConnectSessionCNationSettingManagerQEAAXHZ_1400046EC.h" />
    <ClInclude Include="headers\j_OnDisConnectSessionCHackShieldExSystemUEAAXHZ_1400017DF.h" />
    <ClInclude Include="headers\j_OnDisConnectSessionCNationSettingManagerQEAAXHZ_14000B2E4.h" />
    <ClInclude Include="headers\j_OnLoopSessionCHackShieldExSystemUEAAXHZ_1400137FA.h" />
    <ClInclude Include="headers\j_OnRecvSessionHACKSHEILD_PARAM_ANTICPUEAA_NPEAVCH_140008B89.h" />
    <ClInclude Include="headers\j_OnRecvSession_ClientCheckSum_ResponseHACKSHEILD__140004CA5.h" />
    <ClInclude Include="headers\j_OnRecvSession_ClientCrc_ResponseHACKSHEILD_PARAM_14000338C.h" />
    <ClInclude Include="headers\j_OnRecvSession_ServerCheckSum_RequestHACKSHEILD_P_14000C342.h" />
    <ClInclude Include="headers\j_resizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_140006C12.h" />
    <ClInclude Include="headers\j_SendMsg_CurAllUserLoginCBillingIEAAXXZ_140013D0E.h" />
    <ClInclude Include="headers\j_SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_14000F11E.h" />
    <ClInclude Include="headers\j_SendMsg_LoginCBillingIDMEAA_NPEAD00FPEAU_SYSTEMT_1400078F1.h" />
    <ClInclude Include="headers\j_SendMsg_LoginCBillingJPMEAA_NPEAD00FPEAU_SYSTEMT_14001081B.h" />
    <ClInclude Include="headers\j_SendMsg_LoginCBillingMEAA_NPEAD00FPEAU_SYSTEMTIM_1400040BB.h" />
    <ClInclude Include="headers\j_SendMsg_LoginCBillingNULLMEAA_NPEAD00FPEAU_SYSTE_1400020D1.h" />
    <ClInclude Include="headers\j_SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAX_14000DA17.h" />
    <ClInclude Include="headers\j_Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_14000CA59.h" />
    <ClInclude Include="headers\j_Set_AuthKeyTicketMiningTicketQEAAXIZ_140002F18.h" />
    <ClInclude Include="headers\j_sizelistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_140007054.h" />
    <ClInclude Include="headers\j_sizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_14000AABA.h" />
    <ClInclude Include="headers\j_size_apex_send_loginQEAAHXZ_140013B97.h" />
    <ClInclude Include="headers\j_size_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_co_14000896D.h" />
    <ClInclude Include="headers\j_unchecked_copyPEAV_Iterator0AlistUpairCBHPEAVCAs_14000ECDC.h" />
    <ClInclude Include="headers\j_unchecked_uninitialized_copyPEAV_Iterator0AlistU_140007C34.h" />
    <ClInclude Include="headers\j_unchecked_uninitialized_fill_nPEAV_Iterator0Alis_140003B7F.h" />
    <ClInclude Include="headers\j_UpdateLogFileNameCAsyncLogInfoQEAAXXZ_140006852.h" />
    <ClInclude Include="headers\j_UpdateLogInCompleteCUnmannedTraderControllerQEAA_14000CDE2.h" />
    <ClInclude Include="headers\j_Update_TrunkPasswordCUserDBQEAA_NPEADZ_1400018F7.h" />
    <ClInclude Include="headers\j_ValidateDL_GroupParametersUECPPointCryptoPPCrypt_1400018A2.h" />
    <ClInclude Include="headers\j_ValidateDL_GroupParametersUECPPointCryptoPPCrypt_140007478.h" />
    <ClInclude Include="headers\j_ValidateDL_GroupParametersUECPPointCryptoPPCrypt_14000C37E.h" />
    <ClInclude Include="headers\j_ValidateDL_PrivateKeyImplVDL_GroupParameters_ECV_140009D9F.h" />
    <ClInclude Include="headers\j_ValidateDL_PublicKeyImplVDL_GroupParameters_ECVE_14000AAEC.h" />
    <ClInclude Include="headers\j_validatetable_objlua_tinkerQEAA_NXZ_140009525.h" />
    <ClInclude Include="headers\j_Y_Vector_const_iteratorV_Iterator0AlistUpairCBHP_140003E04.h" />
    <ClInclude Include="headers\j_Y_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAs_14000E15B.h" />
    <ClInclude Include="headers\j__AllocateU_Node_List_nodUpairCBHPEAVCAsyncLogInf_14000E174.h" />
    <ClInclude Include="headers\j__AllocateV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_140007CE3.h" />
    <ClInclude Include="headers\j__BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocat_1400123A5.h" />
    <ClInclude Include="headers\j__BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocat_140012C5B.h" />
    <ClInclude Include="headers\j__BuyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_140004F11.h" />
    <ClInclude Include="headers\j__ConstructPEAU_Node_List_nodUpairCBHPEAVCAsyncLo_140009EE4.h" />
    <ClInclude Include="headers\j__ConstructUpairCBHPEAVCAsyncLogInfostdU12stdYAXP_14000F3A3.h" />
    <ClInclude Include="headers\j__ConstructV_Iterator0AlistUpairCBHPEAVCAsyncLogI_140007D6F.h" />
    <ClInclude Include="headers\j__Construct_nvectorV_Iterator0AlistUpairCBHPEAVCA_14000467E.h" />
    <ClInclude Include="headers\j__Copy_backward_optPEAV_Iterator0AlistUpairCBHPEA_140001271.h" />
    <ClInclude Include="headers\j__Copy_optPEAV_Iterator0AlistUpairCBHPEAVCAsyncLo_14000AB64.h" />
    <ClInclude Include="headers\j__DestroyPEAU_Node_List_nodUpairCBHPEAVCAsyncLogI_140001541.h" />
    <ClInclude Include="headers\j__DestroyU_Node_List_nodUpairCBHPEAVCAsyncLogInfo_14000A9C0.h" />
    <ClInclude Include="headers\j__DestroyvectorV_Iterator0AlistUpairCBHPEAVCAsync_140002CA2.h" />
    <ClInclude Include="headers\j__DestroyV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_14000135C.h" />
    <ClInclude Include="headers\j__Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsync_140001177.h" />
    <ClInclude Include="headers\j__Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsync_14000AF8D.h" />
    <ClInclude Include="headers\j__EMessageAuthenticationCodeImplVHMAC_BaseCryptoP_140002B6C.h" />
    <ClInclude Include="headers\j__EMessageAuthenticationCodeImplVHMAC_BaseCryptoP_14000DE2C.h" />
    <ClInclude Include="headers\j__EMessageAuthenticationCodeImplVHMAC_BaseCryptoP_140013985.h" />
    <ClInclude Include="headers\j__FillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_14000F9D9.h" />
    <ClInclude Include="headers\j__GCAsyncLogInfoQEAAPEAXIZ_14000F1B9.h" />
    <ClInclude Include="headers\j__Get_iter_from_vec_HashV_Hmap_traitsHPEAVCAsyncL_1400066E5.h" />
    <ClInclude Include="headers\j__G_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVal_14000DEAE.h" />
    <ClInclude Include="headers\j__Hashval_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhas_140005F0B.h" />
    <ClInclude Include="headers\j__IncsizelistUpairCBHPEAVCAsyncLogInfostdVallocat_140004CA0.h" />
    <ClInclude Include="headers\j__InsertlistUpairCBHPEAVCAsyncLogInfostdVallocato_140013F34.h" />
    <ClInclude Include="headers\j__InsertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_140002D29.h" />
    <ClInclude Include="headers\j__Insert_nvectorV_Iterator0AlistUpairCBHPEAVCAsyn_140011CD4.h" />
    <ClInclude Include="headers\j__Iter_catV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_14000AA3D.h" />
    <ClInclude Include="headers\j__Iter_randomPEAV_Iterator0AlistUpairCBHPEAVCAsyn_140008332.h" />
    <ClInclude Include="headers\j__Kfn_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareH_140011A90.h" />
    <ClInclude Include="headers\j__Move_backward_optPEAV_Iterator0AlistUpairCBHPEA_14000B69A.h" />
    <ClInclude Include="headers\j__Move_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLo_140003251.h" />
    <ClInclude Include="headers\j__Mynode_Const_iterator0AlistUpairCBHPEAVCAsyncLo_14000F380.h" />
    <ClInclude Include="headers\j__MyvallistUpairCBHPEAVCAsyncLogInfostdVallocator_140003DAF.h" />
    <ClInclude Include="headers\j__NextnodelistUpairCBHPEAVCAsyncLogInfostdValloca_140007F90.h" />
    <ClInclude Include="headers\j__PrevnodelistUpairCBHPEAVCAsyncLogInfostdValloca_1400055BF.h" />
    <ClInclude Include="headers\j__Ptr_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLog_14001394E.h" />
    <ClInclude Include="headers\j__SplicelistUpairCBHPEAVCAsyncLogInfostdVallocato_14000E534.h" />
    <ClInclude Include="headers\j__TidylistUpairCBHPEAVCAsyncLogInfostdVallocatorU_140001555.h" />
    <ClInclude Include="headers\j__TidyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_140012760.h" />
    <ClInclude Include="headers\j__UfillvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1400060F5.h" />
    <ClInclude Include="headers\j__UmovePEAV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_140005F74.h" />
    <ClInclude Include="headers\j__Unchecked_move_backwardPEAV_Iterator0AlistUpair_14000CF40.h" />
    <ClInclude Include="headers\j__Unchecked_uninitialized_movePEAV_Iterator0Alist_140005C68.h" />
    <ClInclude Include="headers\j__Uninit_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyn_14000FCCC.h" />
    <ClInclude Include="headers\j__Uninit_fill_nPEAV_Iterator0AlistUpairCBHPEAVCAs_14000DAC1.h" />
    <ClInclude Include="headers\j__Uninit_movePEAV_Iterator0AlistUpairCBHPEAVCAsyn_140011509.h" />
    <ClInclude Include="headers\j__XlenvectorV_Iterator0AlistUpairCBHPEAVCAsyncLog_14001003C.h" />
    <ClInclude Include="headers\LoginCBillingIDUEAAXPEAVCUserDBZ_14028E0F0.h" />
    <ClInclude Include="headers\LoginCBillingJPUEAAXPEAVCUserDBZ_14028E910.h" />
    <ClInclude Include="headers\LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.h" />
    <ClInclude Include="headers\LoginCBillingNULLUEAAXPEAVCUserDBZ_14028DBD0.h" />
    <ClInclude Include="headers\LoginCBillingUEAAXPEAVCUserDBZ_14028CAC0.h" />
    <ClInclude Include="headers\LogInCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKEPE_1403E0DD0.h" />
    <ClInclude Include="headers\LoginCNormalGuildBattleGuildMemberGUILD_BATTLEQEAA_1403DFA80.h" />
    <ClInclude Include="headers\LogInCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1403E4050.h" />
    <ClInclude Include="headers\LogInCNormalGuildBattleManagerGUILD_BATTLEQEAAXHKK_1403D4360.h" />
    <ClInclude Include="headers\LogInControllServerCNetworkEXAEAA_NHPEADZ_1401C7250.h" />
    <ClInclude Include="headers\LogInWebAgentServerCNetworkEXAEAA_NHPEADZ_1401DA860.h" />
    <ClInclude Include="headers\login_cancel_auto_tradeCMgrAvatorItemHistoryQEAAXH_140239D60.h" />
    <ClInclude Include="headers\lower_bound_HashV_Hmap_traitsHPEAVCAsyncLogInfoVha_1403C30D0.h" />
    <ClInclude Include="headers\make_pairW4ASYNC_LOG_TYPEPEAVCAsyncLogInfostdYAAUp_1403C75D0.h" />
    <ClInclude Include="headers\max_sizeallocatorUpairCBHPEAVCAsyncLogInfostdstdQE_1403C6F00.h" />
    <ClInclude Include="headers\max_sizeallocatorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C7200.h" />
    <ClInclude Include="headers\max_sizelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C5F60.h" />
    <ClInclude Include="headers\max_sizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C69A0.h" />
    <ClInclude Include="headers\NotifyLogInSetBuffCRaceBuffInfoByHolyQuestAEAAXGZ_1403B42D0.h" />
    <ClInclude Include="headers\OnCheckSession_FirstVerifyCHackShieldExSystemUEAA__140417250.h" />
    <ClInclude Include="headers\OnCheckSession_FirstVerifyCNationSettingManagerQEA_140229470.h" />
    <ClInclude Include="headers\OnCheckSession_FirstVerifyHACKSHEILD_PARAM_ANTICPU_140417960.h" />
    <ClInclude Include="headers\OnConnectSessionCHackShieldExSystemUEAAXHZ_1404170D0.h" />
    <ClInclude Include="headers\OnConnectSessionCNationSettingManagerQEAAXHZ_140229400.h" />
    <ClInclude Include="headers\OnDisConnectSessionCHackShieldExSystemUEAAXHZ_140417140.h" />
    <ClInclude Include="headers\OnDisConnectSessionCNationSettingManagerQEAAXHZ_1402294F0.h" />
    <ClInclude Include="headers\OnLoopSessionCHackShieldExSystemUEAAXHZ_1404171A0.h" />
    <ClInclude Include="headers\OnRecvSessionHACKSHEILD_PARAM_ANTICPUEAA_NPEAVCHac_140417F10.h" />
    <ClInclude Include="headers\OnRecvSession_ClientCheckSum_ResponseHACKSHEILD_PA_140418120.h" />
    <ClInclude Include="headers\OnRecvSession_ClientCrc_ResponseHACKSHEILD_PARAM_A_140418290.h" />
    <ClInclude Include="headers\OnRecvSession_ServerCheckSum_RequestHACKSHEILD_PAR_140417FB0.h" />
    <ClInclude Include="headers\R3InvalidateDeviceYAJXZ_1404E9FC0.h" />
    <ClInclude Include="headers\resizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C3F20.h" />
    <ClInclude Include="headers\SendMsg_CurAllUserLoginCBillingIEAAXXZ_14028D610.h" />
    <ClInclude Include="headers\SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_1402570F0.h" />
    <ClInclude Include="headers\SendMsg_LoginCBillingIDMEAA_NPEAD00FPEAU_SYSTEMTIM_14028E600.h" />
    <ClInclude Include="headers\SendMsg_LoginCBillingJPMEAA_NPEAD00FPEAU_SYSTEMTIM_14028ECC0.h" />
    <ClInclude Include="headers\SendMsg_LoginCBillingMEAA_NPEAD00FPEAU_SYSTEMTIMEJ_14028D3C0.h" />
    <ClInclude Include="headers\SendMsg_LoginCBillingNULLMEAA_NPEAD00FPEAU_SYSTEMT_14028DC10.h" />
    <ClInclude Include="headers\SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAXHZ_1402D8540.h" />
    <ClInclude Include="headers\SetOccDialogInfoCDialogMEAAHPEAU_AFX_OCC_DIALOG_IN_1404DBD42.h" />
    <ClInclude Include="headers\SetOccDialogInfoCFormViewMEAAHPEAU_AFX_OCC_DIALOG__1404DC156.h" />
    <ClInclude Include="headers\SetOccDialogInfoCWndMEAAHPEAU_AFX_OCC_DIALOG_INFOZ_1404DBE1A.h" />
    <ClInclude Include="headers\Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_1400A6BA0.h" />
    <ClInclude Include="headers\Set_AuthKeyTicketMiningTicketQEAAXIZ_140078ED0.h" />
    <ClInclude Include="headers\sizelistUpairCBHPEAVCAsyncLogInfostdVallocatorUpai_1403C4650.h" />
    <ClInclude Include="headers\sizevectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C4210.h" />
    <ClInclude Include="headers\size_apex_send_loginQEAAHXZ_140410BF0.h" />
    <ClInclude Include="headers\size_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash_comp_1403C3080.h" />
    <ClInclude Include="headers\unchecked_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyn_1403C7970.h" />
    <ClInclude Include="headers\unchecked_uninitialized_copyPEAV_Iterator0AlistUpa_1403C8D20.h" />
    <ClInclude Include="headers\unchecked_uninitialized_fill_nPEAV_Iterator0AlistU_1403C7C50.h" />
    <ClInclude Include="headers\UpdateLogFileNameCAsyncLogInfoQEAAXXZ_1403BD0F0.h" />
    <ClInclude Include="headers\UpdateLogInCompleteCUnmannedTraderControllerQEAAEP_14034E440.h" />
    <ClInclude Include="headers\Update_TrunkPasswordCUserDBQEAA_NPEADZ_140116FD0.h" />
    <ClInclude Include="headers\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAD0.h" />
    <ClInclude Include="headers\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAF0.h" />
    <ClInclude Include="headers\ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADD90.h" />
    <ClInclude Include="headers\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A900.h" />
    <ClInclude Include="headers\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046A920.h" />
    <ClInclude Include="headers\ValidateDL_GroupParametersUECPPointCryptoPPCryptoP_14046AD80.h" />
    <ClInclude Include="headers\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_140551AC0.h" />
    <ClInclude Include="headers\ValidateDL_GroupParametersVIntegerCryptoPPCryptoPP_1405AD4F0.h" />
    <ClInclude Include="headers\ValidateDL_PrivateKeyImplVDL_GroupParameters_DSACr_140568460.h" />
    <ClInclude Include="headers\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_1404515F0.h" />
    <ClInclude Include="headers\ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_140558CA0.h" />
    <ClInclude Include="headers\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFPCr_140635EE0.h" />
    <ClInclude Include="headers\ValidateDL_PrivateKeyImplVDL_GroupParameters_GFP_D_140637C30.h" />
    <ClInclude Include="headers\ValidateDL_PublicKeyImplVDL_GroupParameters_DSACry_140568F30.h" />
    <ClInclude Include="headers\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVEC2_140558420.h" />
    <ClInclude Include="headers\ValidateDL_PublicKeyImplVDL_GroupParameters_ECVECP_140450870.h" />
    <ClInclude Include="headers\ValidateDL_PublicKeyImplVDL_GroupParameters_GFPCry_1406369F0.h" />
    <ClInclude Include="headers\ValidateDL_PublicKeyImplVDL_GroupParameters_GFP_De_1406373A0.h" />
    <ClInclude Include="headers\ValidateElementDL_GroupParameters_ECVEC2NCryptoPPC_140583CF0.h" />
    <ClInclude Include="headers\ValidateElementDL_GroupParameters_ECVECPCryptoPPCr_14057FB10.h" />
    <ClInclude Include="headers\ValidateElementDL_GroupParameters_IntegerBasedCryp_140630AE0.h" />
    <ClInclude Include="headers\ValidateGroupDL_GroupParameters_DSACryptoPPUEBA_NA_140630230.h" />
    <ClInclude Include="headers\ValidateGroupDL_GroupParameters_ECVEC2NCryptoPPCry_1405835A0.h" />
    <ClInclude Include="headers\ValidateGroupDL_GroupParameters_ECVECPCryptoPPCryp_14057F300.h" />
    <ClInclude Include="headers\ValidateGroupDL_GroupParameters_IntegerBasedCrypto_140630680.h" />
    <ClInclude Include="headers\ValidateParametersEC2NCryptoPPQEBA_NAEAVRandomNumb_14062E210.h" />
    <ClInclude Include="headers\ValidateParametersECPCryptoPPQEBA_NAEAVRandomNumbe_14060E2A0.h" />
    <ClInclude Include="headers\validatetable_objlua_tinkerQEAA_NXZ_1404462F0.h" />
    <ClInclude Include="headers\Y_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C7420.h" />
    <ClInclude Include="headers\Y_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6DD0.h" />
    <ClInclude Include="headers\_AllocateU_Node_List_nodUpairCBHPEAVCAsyncLogInfos_1403C7E90.h" />
    <ClInclude Include="headers\_AllocateV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7D00.h" />
    <ClInclude Include="headers\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6370.h" />
    <ClInclude Include="headers\_BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6500.h" />
    <ClInclude Include="headers\_BuyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C7100.h" />
    <ClInclude Include="headers\_CAsyncLogInfoInit__1_dtor0_1403BD0C0.h" />
    <ClInclude Include="headers\_CAsyncLogInfo_CAsyncLogInfo__1_dtor0_1403BCB50.h" />
    <ClInclude Include="headers\_CEnglandBillingMgrCallFunc_RFOnline_Auth__1_dtor0_140319C50.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLogIn__1_dtor0_1403A5C90.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLogIn__1_dtor1_1403A5CC0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLogIn__1_dtor2_1403A5CF0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLogIn__1_dtor3_1403A5D20.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLogIn__1_dtor0_1403AD090.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLogIn__1_dtor1_1403AD0C0.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLogIn__1_dtor2_1403AD0F0.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLogIn__1_dtor3_1403AD120.h" />
    <ClInclude Include="headers\_ConstructPEAU_Node_List_nodUpairCBHPEAVCAsyncLogI_1403C7F50.h" />
    <ClInclude Include="headers\_ConstructUpairCBHPEAVCAsyncLogInfostdU12stdYAXPEA_1403C7DB0.h" />
    <ClInclude Include="headers\_ConstructV_Iterator0AlistUpairCBHPEAVCAsyncLogInf_1403C8B70.h" />
    <ClInclude Include="headers\_Construct_nvectorV_Iterator0AlistUpairCBHPEAVCAsy_1403C6820.h" />
    <ClInclude Include="headers\_Copy_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8AD0.h" />
    <ClInclude Include="headers\_Copy_optPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8530.h" />
    <ClInclude Include="headers\_CryptoPPDL_PrivateKeyImpl_CryptoPPDL_GroupParamet_140451850.h" />
    <ClInclude Include="headers\_DestroyPEAU_Node_List_nodUpairCBHPEAVCAsyncLogInf_1403C7BC0.h" />
    <ClInclude Include="headers\_DestroyU_Node_List_nodUpairCBHPEAVCAsyncLogInfost_1403C7F40.h" />
    <ClInclude Include="headers\_DestroyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C69F0.h" />
    <ClInclude Include="headers\_DestroyV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8C60.h" />
    <ClInclude Include="headers\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C7BD0.h" />
    <ClInclude Include="headers\_Destroy_rangeV_Iterator0AlistUpairCBHPEAVCAsyncLo_1403C8810.h" />
    <ClInclude Include="headers\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_140465900.h" />
    <ClInclude Include="headers\_EMessageAuthenticationCodeImplVHMAC_BaseCryptoPPV_14046AC80.h" />
    <ClInclude Include="headers\_FillPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfos_1403C8680.h" />
    <ClInclude Include="headers\_GCAsyncLogInfoQEAAPEAXIZ_1403C14F0.h" />
    <ClInclude Include="headers\_Get_iter_from_vec_HashV_Hmap_traitsHPEAVCAsyncLog_1403C2E50.h" />
    <ClInclude Include="headers\_G_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdVallo_1403C8CB0.h" />
    <ClInclude Include="headers\_Hashval_HashV_Hmap_traitsHPEAVCAsyncLogInfoVhash__1403C3530.h" />
    <ClInclude Include="headers\_IncsizelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C4D90.h" />
    <ClInclude Include="headers\_InsertlistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C4670.h" />
    <ClInclude Include="headers\_InsertV_Iterator0AlistUpairCBHPEAVCAsyncLogInfost_1403C80C0.h" />
    <ClInclude Include="headers\_Insert_nvectorV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C5320.h" />
    <ClInclude Include="headers\_Iter_catV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C8060.h" />
    <ClInclude Include="headers\_Iter_randomPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8470.h" />
    <ClInclude Include="headers\_Kfn_Hmap_traitsHPEAVCAsyncLogInfoVhash_compareHUl_1403C35E0.h" />
    <ClInclude Include="headers\_Move_backward_optPEAV_Iterator0AlistUpairCBHPEAVC_1403C8760.h" />
    <ClInclude Include="headers\_Move_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C8700.h" />
    <ClInclude Include="headers\_Mynode_Const_iterator0AlistUpairCBHPEAVCAsyncLogI_1403C5C50.h" />
    <ClInclude Include="headers\_MyvallistUpairCBHPEAVCAsyncLogInfostdVallocatorUp_1403C3610.h" />
    <ClInclude Include="headers\_NextnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C3600.h" />
    <ClInclude Include="headers\_PrevnodelistUpairCBHPEAVCAsyncLogInfostdVallocato_1403C4590.h" />
    <ClInclude Include="headers\_Ptr_catPEAV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C84D0.h" />
    <ClInclude Include="headers\_SplicelistUpairCBHPEAVCAsyncLogInfostdVallocatorU_1403C38D0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C18C0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2480.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24B0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C24E0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2510.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2540.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2570.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25A0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C25D0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2600.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2630.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2660.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2690.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C26C0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2700.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FA0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C2FD0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3000.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3370.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33A0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C33D0.h" />
    <ClInclude Include="headers\_stdext_Hash_stdext_Hmap_traits_int_CAsyncLogInfo__1403C3410.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3820.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3850.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3C90.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CC0.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3CF0.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D20.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D50.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C3D80.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4730.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4A70.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AA0.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4AD0.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B10.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B50.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4B90.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C4E80.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6130.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6160.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6190.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6440.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C6600.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7310.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7340.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7800.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7830.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7860.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C7890.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C78C0.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8200.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8230.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8260.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8290.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C82C0.h" />
    <ClInclude Include="headers\_stdlist_stdpair_int_const__CAsyncLogInfo_____ptr6_1403C8390.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C40F0.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4120.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C4150.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5150.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5180.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C51B0.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5860.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5890.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C58C0.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C5920.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C68B0.h" />
    <ClInclude Include="headers\_stdvector_stdlist_stdpair_int_const__CAsyncLogInf_1403C6B80.h" />
    <ClInclude Include="headers\_std_Construct_stdlist_stdpair_int_const__CAsyncLo_1403C8C00.h" />
    <ClInclude Include="headers\_std_Uninit_copy_stdlist_stdpair_int_const__CAsync_1403C8E60.h" />
    <ClInclude Include="headers\_std_Uninit_fill_n_stdlist_stdpair_int_const__CAsy_1403C8920.h" />
    <ClInclude Include="headers\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D10.h" />
    <ClInclude Include="headers\_std_Vector_iterator_stdlist_stdpair_int_const__CA_1403C5D40.h" />
    <ClInclude Include="headers\_TidylistUpairCBHPEAVCAsyncLogInfostdVallocatorUpa_1403C4CC0.h" />
    <ClInclude Include="headers\_TidyvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C5240.h" />
    <ClInclude Include="headers\_UfillvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogI_1403C6A60.h" />
    <ClInclude Include="headers\_UmovePEAV_Iterator0AlistUpairCBHPEAVCAsyncLogInfo_1403C7A30.h" />
    <ClInclude Include="headers\_Unchecked_move_backwardPEAV_Iterator0AlistUpairCB_1403C7B00.h" />
    <ClInclude Include="headers\_Unchecked_uninitialized_movePEAV_Iterator0AlistUp_1403C85D0.h" />
    <ClInclude Include="headers\_Uninit_copyPEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8DD0.h" />
    <ClInclude Include="headers\_Uninit_fill_nPEAV_Iterator0AlistUpairCBHPEAVCAsyn_1403C8890.h" />
    <ClInclude Include="headers\_Uninit_movePEAV_Iterator0AlistUpairCBHPEAVCAsyncL_1403C8A60.h" />
    <ClInclude Include="headers\_ValidateImageBase_1404DE4C0.h" />
    <ClInclude Include="headers\_XlenvectorV_Iterator0AlistUpairCBHPEAVCAsyncLogIn_1403C6AF0.h" />
  </ItemGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>