/*
 * BillingSystem.h
 * 
 * Header for billing system authentication functions
 * Handles various billing provider integrations and login operations
 */

#pragma once
#ifndef BILLINGSYSTEM_H
#define BILLINGSYSTEM_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Billing {

// Forward declarations
class CUserDB;
class CBillingManager;
class CBillingID;
class CBillingJP;
class CBillingNULL;
class CBilling;
class CEnglandBillingMgr;
class CRusiaBillingMgr;

/**
 * Standard billing login function
 * @param this_ptr Pointer to CBilling object
 * @param user_db Pointer to user database object
 */
void __fastcall BillingLogin(CBilling* this_ptr, CUserDB* user_db);

/**
 * Billing manager login function
 * @param this_ptr Pointer to CBillingManager object
 * @param user_db Pointer to user database object
 */
void __fastcall BillingManagerLogin(CBillingManager* this_ptr, CUserDB* user_db);

/**
 * Billing ID provider login function
 * @param this_ptr Pointer to CBillingID object
 * @param user_db Pointer to user database object
 */
void __fastcall BillingIDLogin(CBillingID* this_ptr, CUserDB* user_db);

/**
 * Japan billing provider login function
 * @param this_ptr Pointer to CBillingJP object
 * @param user_db Pointer to user database object
 */
void __fastcall JapanBillingLogin(CBillingJP* this_ptr, CUserDB* user_db);

/**
 * NULL billing provider login function (for testing/free servers)
 * @param this_ptr Pointer to CBillingNULL object
 * @param user_db Pointer to user database object
 */
void __fastcall NullBillingLogin(CBillingNULL* this_ptr, CUserDB* user_db);

/**
 * England billing manager authentication function
 * @param this_ptr Pointer to CEnglandBillingMgr object
 * @param param1 Authentication parameter 1
 * @param param2 Authentication parameter 2
 * @return Authentication result
 */
int __fastcall EnglandBillingAuth(CEnglandBillingMgr* this_ptr, void* param1, void* param2);

/**
 * Russia billing manager authentication function
 * @param this_ptr Pointer to CRusiaBillingMgr object
 * @param param1 Authentication parameter 1
 * @param param2 Authentication parameter 2
 * @return Authentication result
 */
int __fastcall RusiaBillingAuth(CRusiaBillingMgr* this_ptr, void* param1, void* param2);

} // namespace Billing
} // namespace Authentication
} // namespace RFOnline

#endif // BILLINGSYSTEM_H
