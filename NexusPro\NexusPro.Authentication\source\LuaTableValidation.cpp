/*
 * LuaTableValidation.cpp
 * 
 * Lua table validation functionality for RF Online authentication
 * Handles validation of Lua table objects used in scripting system
 * 
 * Original Function: lua_tinker::table_obj::validate
 * Original Address: 0x1404462F0
 * Original File: validatetable_objlua_tinkerQEAA_NXZ_1404462F0.c
 */

#include "../headers/LuaTableValidation.h"
#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include <cstdint>

namespace RFOnline {
namespace Authentication {
namespace Scripting {

/*
 * Validate Lua table object
 * Address: 0x1404462F0
 * Purpose: Validates that a Lua table object is still valid and accessible
 */
bool __fastcall ValidateLuaTable(lua_tinker::table_obj* this_ptr)
{
    __int64* buffer_ptr;           // Original: v1 (rdi register)
    signed __int64 loop_counter;   // Original: i (rcx register)
    const void* pointer_check;     // Original: v3 (rax register)
    bool validation_result;        // Original: result (al register)
    const void* secondary_check;   // Original: v5 (rax register)
    __int64 stack_buffer[12];      // Original: v6 (stack buffer [sp+0h] [bp-38h])
    int lua_type;                  // Original: v7 ([sp+20h] [bp-18h])
    unsigned int iteration_count;  // Original: j ([sp+24h] [bp-14h])
    lua_tinker::table_obj* table_object; // Original: v9 ([sp+40h] [bp+8h])

    // Initialize local variables
    table_object = this_ptr;
    buffer_ptr = stack_buffer;
    
    // Initialize memory with debug pattern (original IDA pattern)
    for (loop_counter = 12LL; loop_counter; --loop_counter)
    {
        *reinterpret_cast<uint32_t*>(buffer_ptr) = 0xCCCCCCCC; // -858993460 in hex
        buffer_ptr = reinterpret_cast<__int64*>(reinterpret_cast<char*>(buffer_ptr) + 4);
    }
    
    // Check if table object has a valid pointer
    if (table_object->m_pointer)
    {
        // Get pointer from Lua state at the specified index
        pointer_check = lua_topointer(table_object->m_L, table_object->m_index);
        
        // Verify that the stored pointer matches the current Lua pointer
        if (table_object->m_pointer == pointer_check)
        {
            validation_result = true;
        }
        else
        {
            // Pointer mismatch - table may have been garbage collected or moved
            lua_type = lua_type(table_object->m_L, table_object->m_index);
            
            // Iterate through possible indices to find the table
            for (iteration_count = 1; iteration_count <= 0x3E8; ++iteration_count) // 1000 iterations max
            {
                // Check if we can find the table at a different index
                secondary_check = lua_topointer(table_object->m_L, iteration_count);
                if (table_object->m_pointer == secondary_check)
                {
                    // Found the table at a different index - update our reference
                    table_object->m_index = iteration_count;
                    validation_result = true;
                    goto validation_complete;
                }
            }
            
            // Table not found - mark as invalid
            validation_result = false;
        }
    }
    else
    {
        // No pointer stored - table is invalid
        validation_result = false;
    }

validation_complete:
    return validation_result;
}

} // namespace Scripting
} // namespace Authentication
} // namespace RFOnline
