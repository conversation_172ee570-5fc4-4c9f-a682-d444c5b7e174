/**
 * @file ValidateECPParameters.cpp
 * @brief RF Online ECP (Elliptic Curve Prime) Parameter Validation Function
 * @note Original Function: ?ValidateParameters@ECP@CryptoPP@@QEBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x14060E2A0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: ValidateParametersECPCryptoPPQEBA_NAEAVRandomNumbe_14060E2A0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * @brief Validates ECP (Elliptic Curve Prime) cryptographic parameters
 * @param this Pointer to ECP instance
 * @param randomGenerator Pointer to random number generator for validation
 * @param validationLevel Level of validation to perform (1=basic, 2=prime verification)
 * @return char Returns 1 if parameters are valid, 0 if invalid
 * 
 * This function performs comprehensive validation of elliptic curve parameters
 * used in cryptographic operations. It validates:
 * 1. Field size and parameter ranges
 * 2. Curve equation validity (4a³ + 27b² ≠ 0 mod p)
 * 3. Prime field verification (if validation level >= 2)
 */
char __fastcall CryptoPP::ECP::ValidateParameters(
    CryptoPP::ECP* this,
    struct CryptoPP::RandomNumberGenerator* randomGenerator,
    unsigned int validationLevel) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    unsigned int level;                        // Original: v3 (er9 register)
    CryptoPP::Integer fieldSize;               // Original: b ([sp+20h] [bp-248h])
    char validationResult;                     // Original: v6 ([sp+48h] [bp-220h])
    CryptoPP::Integer twentySeven;             // Original: a ([sp+50h] [bp-218h])
    CryptoPP::Integer tempResult;              // Original: result ([sp+78h] [bp-1F0h])
    CryptoPP::Integer temp1, temp2, temp3;     // Original: v9, v10, v11 ([sp+A0h] to [sp+F0h])
    CryptoPP::Integer temp4, temp5, temp6;     // Original: v12, v13, v14 ([sp+118h] to [sp+168h])
    CryptoPP::Integer finalResult;             // Original: v15 ([sp+190h] [bp-D8h])
    char returnValue;                          // Original: v16 ([sp+1B8h] [bp-B0h])
    int destructorFlags;                       // Original: v17 ([sp+1BCh] [bp-ACh])
    __int64 stackGuard;                        // Original: v18 ([sp+1C0h] [bp-A8h])
    int basicValidation;                       // Original: v19 ([sp+1C8h] [bp-A0h])
    int advancedValidation;                    // Original: v37 ([sp+258h] [bp-10h])
    int primeValidation;                       // Original: v38 ([sp+25Ch] [bp-Ch])
    CryptoPP::ECP* ecpInstance;                // Original: v39 ([sp+270h] [bp+8h])
    CryptoPP* cryptoInstance;                  // Original: v40 ([sp+278h] [bp+10h])
    unsigned int level_copy;                   // Original: v41 ([sp+280h] [bp+18h])

    // Initialize local variables
    level_copy = validationLevel;
    cryptoInstance = reinterpret_cast<CryptoPP*>(randomGenerator);
    ecpInstance = this;
    stackGuard = -2LL;
    destructorFlags = 0;

    // Get the field size for this elliptic curve
    CryptoPP::ECP::FieldSize(this, &fieldSize);
    
    // Check if field size is odd (required for prime fields)
    validationResult = CryptoPP::Integer::IsOdd(&fieldSize);
    
    // Perform basic parameter validation
    basicValidation = validationResult
        && !CryptoPP::Integer::IsNegative(&ecpInstance->m_a)      // a >= 0
        && CryptoPP::operator<(&ecpInstance->m_a, &fieldSize)     // a < p
        && !CryptoPP::Integer::IsNegative(&ecpInstance->m_b)      // b >= 0
        && CryptoPP::operator<(&ecpInstance->m_b, &fieldSize);    // b < p
    
    validationResult = basicValidation;
    
    // If validation level >= 1, perform curve discriminant validation
    if (level_copy >= 1) {
        try {
            // Calculate discriminant: 4a³ + 27b² mod p
            // This must be non-zero for a valid elliptic curve
            
            // Create integer constants
            CryptoPP::Integer::Integer(&twentySeven, 27);
            destructorFlags |= 1u;
            
            CryptoPP::Integer::Integer(&temp1, 4);
            destructorFlags |= 2u;
            
            // Calculate 27b²
            CryptoPP::Integer* bSquared = CryptoPP::operator*(&tempResult, &twentySeven, &ecpInstance->m_b);
            destructorFlags |= 4u;
            
            CryptoPP::Integer* bCubed = CryptoPP::operator*(&temp2, bSquared, &ecpInstance->m_b);
            destructorFlags |= 8u;
            
            // Calculate 4a³
            CryptoPP::Integer* aSquared = CryptoPP::operator*(&temp3, &temp1, &ecpInstance->m_a);
            destructorFlags |= 0x10u;
            
            CryptoPP::Integer* aCubed1 = CryptoPP::operator*(&temp4, aSquared, &ecpInstance->m_a);
            destructorFlags |= 0x20u;
            
            CryptoPP::Integer* aCubed2 = CryptoPP::operator*(&temp5, aCubed1, &ecpInstance->m_a);
            destructorFlags |= 0x40u;
            
            // Add 4a³ + 27b²
            CryptoPP::Integer* discriminant = CryptoPP::operator+(&temp6, aCubed2, bCubed);
            destructorFlags |= 0x80u;
            
            // Take modulo p
            CryptoPP::Integer* finalDiscriminant = CryptoPP::operator%(&finalResult, discriminant, &fieldSize);
            destructorFlags |= 0x100u;
            
            // Check if discriminant is positive (non-zero)
            advancedValidation = CryptoPP::Integer::IsPositive(finalDiscriminant);
            
            validationResult = advancedValidation;
            
            // Clean up temporary integers in reverse order
            if (destructorFlags & 0x100) {
                destructorFlags &= 0xFFFFFEFF;
                CryptoPP::Integer::~Integer(&finalResult);
            }
            if (destructorFlags & 0x80) {
                destructorFlags &= 0xFFFFFF7F;
                CryptoPP::Integer::~Integer(&temp6);
            }
            if (destructorFlags & 0x40) {
                destructorFlags &= 0xFFFFFFBF;
                CryptoPP::Integer::~Integer(&temp5);
            }
            if (destructorFlags & 0x20) {
                destructorFlags &= 0xFFFFFFDF;
                CryptoPP::Integer::~Integer(&temp4);
            }
            if (destructorFlags & 0x10) {
                destructorFlags &= 0xFFFFFFEF;
                CryptoPP::Integer::~Integer(&temp3);
            }
            if (destructorFlags & 8) {
                destructorFlags &= 0xFFFFFFF7;
                CryptoPP::Integer::~Integer(&temp2);
            }
            if (destructorFlags & 4) {
                destructorFlags &= 0xFFFFFFFB;
                CryptoPP::Integer::~Integer(&tempResult);
            }
            if (destructorFlags & 2) {
                destructorFlags &= 0xFFFFFFFD;
                CryptoPP::Integer::~Integer(&temp1);
            }
            if (destructorFlags & 1) {
                destructorFlags &= 0xFFFFFFFE;
                CryptoPP::Integer::~Integer(&twentySeven);
            }
        } catch (...) {
            validationResult = 0;  // Validation failed due to exception
        }
    }
    
    // If validation level >= 2, perform prime verification
    if (level_copy >= 2) {
        primeValidation = validationResult
            && CryptoPP::VerifyPrime(
                cryptoInstance,
                reinterpret_cast<struct CryptoPP::RandomNumberGenerator*>(&fieldSize),
                reinterpret_cast<const struct CryptoPP::Integer*>(1),
                level);
        validationResult = primeValidation;
    }
    
    returnValue = validationResult;
    
    // Clean up field size integer
    CryptoPP::Integer::~Integer(&fieldSize);
    
    return returnValue;
}
