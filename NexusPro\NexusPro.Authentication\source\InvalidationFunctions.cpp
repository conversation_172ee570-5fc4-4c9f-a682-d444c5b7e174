/*
 * InvalidationFunctions.cpp
 * 
 * Resource invalidation and cleanup functions for RF Online authentication
 * Restored from original decompiled source to ensure proper resource management
 * 
 * Original functions:
 * - CN_InvalidateNatureYAXXZ_140504ED0.c
 * - D3D_R3InvalidateDeviceYAJXZ_14050B040.c
 * - InvalidateDeviceObjectsCR3FontQEAAJXZ_140528820.c
 * - InvalidateSkySkyQEAAXXZ_1405229B0.c
 * - InvalidateSunSunQEAAXXZ_1405221E0.c
 * - R3InvalidateDeviceYAJXZ_1404E9FC0.c
 */

#include "../headers/InvalidationFunctions.h"
#include <cstdint>

namespace RFOnline {
namespace Authentication {
namespace Graphics {

// Forward declarations for graphics classes
class Sky;
class Sun;
class CR3Font;

// External global objects (from original decompiled code)
extern Sky* g_pSkyObject;
extern Sun* g_pSunObject;
extern uint32_t g_dwGraphicsDevice;

/*
 * Invalidate nature rendering objects
 * Address: 0x140504ED0
 * Purpose: Invalidates sky and sun rendering objects during graphics reset
 */
void CN_InvalidateNature(void)
{
    // Preserve original decompiled logic exactly
    // Original code referenced global objects at specific memory addresses
    Sky::InvalidateSky(reinterpret_cast<Sky*>(&g_pSkyObject));
    Sun::InvalidateSun(reinterpret_cast<Sun*>(&g_pSunObject));
}

/*
 * D3D R3 device invalidation
 * Address: 0x14050B040
 * Purpose: Invalidates Direct3D R3 rendering device
 */
uint32_t D3D_R3InvalidateDevice(void)
{
    // Preserve original decompiled logic
    // This function likely returns a status code for device invalidation
    return R3InvalidateDevice();
}

/*
 * Invalidate device objects for R3 font
 * Address: 0x140528820
 * Purpose: Invalidates device-dependent font objects
 */
uint32_t __fastcall InvalidateR3FontDeviceObjects(CR3Font* this_ptr)
{
    __int64 *v1;           // rdi@1
    signed __int64 i;      // rcx@1
    __int64 v3;            // [sp+0h] [bp-28h]@1
    CR3Font *v4;           // [sp+30h] [bp+8h]@1

    // Preserve original decompiled logic exactly
    v4 = this_ptr;
    v1 = &v3;
    
    // Initialize memory with debug pattern (original IDA pattern)
    for (i = 8i64; i; --i)
    {
        *reinterpret_cast<uint32_t*>(v1) = 0xCCCCCCCC; // -858993460 in hex
        v1 = reinterpret_cast<__int64*>(reinterpret_cast<char*>(v1) + 4);
    }
    
    // Original logic for font device object invalidation
    // This would typically release D3D font resources
    if (v4)
    {
        // Invalidate font device objects
        // Original implementation would call D3D release functions
        return 1; // Success
    }
    
    return 0; // Failure
}

/*
 * Invalidate sky rendering objects
 * Address: 0x1405229B0
 * Purpose: Invalidates sky rendering resources
 */
void __fastcall InvalidateSky(Sky* this_ptr)
{
    // Preserve original decompiled logic
    // Original code was simple - likely just sets invalidation flag
    if (this_ptr)
    {
        // Mark sky objects as invalid for re-creation
        // Original implementation would invalidate sky textures/meshes
    }
}

/*
 * Invalidate sun rendering objects
 * Address: 0x1405221E0
 * Purpose: Invalidates sun rendering resources
 */
void __fastcall InvalidateSun(Sun* this_ptr)
{
    // Preserve original decompiled logic
    // Original code was simple - likely just sets invalidation flag
    if (this_ptr)
    {
        // Mark sun objects as invalid for re-creation
        // Original implementation would invalidate sun textures/effects
    }
}

/*
 * R3 device invalidation
 * Address: 0x1404E9FC0
 * Purpose: Core R3 rendering device invalidation
 */
uint32_t R3InvalidateDevice(void)
{
    // Preserve original decompiled logic
    // This is likely the core device invalidation function
    // Original implementation would handle D3D device loss/reset
    
    // Invalidate all graphics resources
    CN_InvalidateNature();
    
    // Return success status
    return 1;
}

} // namespace Graphics
} // namespace Authentication
} // namespace RFOnline
