/*
 * ServerConnections.h
 * 
 * Header for server connection and login functionality
 * Handles control server, web agent server, and various server login operations
 */

#pragma once
#ifndef SERVERCONNECTIONS_H
#define SERVERCONNECTIONS_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Server {

// Forward declarations
class CNetworkEX;
class CUnmannedTraderController;
class CUnmannedTraderUserInfo;

/**
 * Login to control server
 * Handles authentication and connection to the control server
 * @param this_ptr Pointer to CNetworkEX object
 * @param session_id Session identifier
 * @param login_data Login data pointer
 * @return Login result
 */
bool __fastcall LoginControlServer(CNetworkEX* this_ptr, int session_id, void* login_data);

/**
 * Login to web agent server
 * Handles authentication and connection to the web agent server
 * @param this_ptr Pointer to CNetworkEX object
 * @param session_id Session identifier
 * @param login_data Login data pointer
 * @return Login result
 */
bool __fastcall LoginWebAgentServer(CNetworkEX* this_ptr, int session_id, void* login_data);

/**
 * Complete login for compete functionality
 * Handles completion of login process for competitive features
 * @param this_ptr Pointer to CUnmannedTraderController object
 * @param user_data User data for completion
 */
void __fastcall CompleteLoginCompete(CUnmannedTraderController* this_ptr, void* user_data);

/**
 * Update login completion status
 * Updates the login completion status for unmanned trader
 * @param this_ptr Pointer to CUnmannedTraderController object
 * @param completion_data Completion status data
 * @param additional_param Additional parameter
 */
void __fastcall UpdateLoginComplete(CUnmannedTraderController* this_ptr, void* completion_data, void* additional_param);

/**
 * Check login state for unmanned trader
 * Verifies the current login state of unmanned trader user
 * @param this_ptr Pointer to CUnmannedTraderUserInfo object
 * @return True if logged in, false otherwise
 */
bool __fastcall IsLoginState(CUnmannedTraderUserInfo* this_ptr);

} // namespace Server
} // namespace Authentication
} // namespace RFOnline

#endif // SERVERCONNECTIONS_H
