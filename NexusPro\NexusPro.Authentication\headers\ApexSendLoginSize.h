/*
 * ApexSendLoginSize.h
 * 
 * Header for Apex anti-cheat login size functionality
 * Handles size calculations for Apex login packets
 */

#pragma once
#ifndef APEXSENDLOGINSIZE_H
#define APEXSENDLOGINSIZE_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace AntiCheat {

/**
 * Get the size for Apex anti-cheat login packet
 * @return Size of the login packet in bytes
 */
int __fastcall GetApexSendLoginSize(void);

} // namespace AntiCheat
} // namespace Authentication
} // namespace RFOnline

#endif // APEXSENDLOGINSIZE_H
