/*
 * GenerateEphemeralKeyPair.cpp
 * Original Functions:
 *   - AuthenticatedKeyAgreementDomain::GenerateEphemeralKeyPair (0x1405F6600)
 *   - AuthenticatedKeyAgreementDomain::GenerateStaticKeyPair (0x1405F65A0)
 *
 * Description: Generates ephemeral and static key pairs for authenticated key agreement.
 * These functions create cryptographic keys used in secure communication protocols.
 * Original Files:
 *   - GenerateEphemeralKeyPairAuthenticatedKeyAgreementD_1405F6600.c
 *   - GenerateStaticKeyPairAuthenticatedKeyAgreementDoma_1405F65A0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Generate ephemeral key pair for authenticated key agreement
void CryptoPP::AuthenticatedKeyAgreementDomain::GenerateEphemeralKeyPair(
    CryptoPP::AuthenticatedKeyAgreementDomain *this, 
    struct CryptoPP::RandomNumberGenerator *a2, 
    unsigned __int8 *a3, 
    unsigned __int8 *a4) {
    
    CryptoPP::AuthenticatedKeyAgreementDomain *v4; // This pointer [sp+30h] [bp+8h]
    struct CryptoPP::RandomNumberGenerator *v5;    // Random number generator [sp+38h] [bp+10h]
    unsigned __int8 *v6;                          // Private key buffer [sp+40h] [bp+18h]
    unsigned __int8 *v7;                          // Public key buffer [sp+48h] [bp+20h]

    v7 = a4;  // Public key output buffer
    v6 = a3;  // Private key output buffer
    v5 = a2;  // Random number generator
    v4 = this;

    // Call virtual function to prepare for key generation
    ((void (*)(void))this->vfptr[7].Clone)();
    
    // Call virtual function to generate the actual key pair
    ((void (*)(CryptoPP::AuthenticatedKeyAgreementDomain *, struct CryptoPP::RandomNumberGenerator *, unsigned __int8 *, unsigned __int8 *))v4->vfptr[8].__vecDelDtor)(
        v4,  // This pointer
        v5,  // Random number generator
        v6,  // Private key buffer
        v7); // Public key buffer
}

/**
 * @brief Generates a static key pair for authenticated key agreement
 * @param this Pointer to AuthenticatedKeyAgreementDomain instance
 * @param randomGenerator Pointer to random number generator
 * @param privateKey Pointer to buffer for private key output
 * @param publicKey Pointer to buffer for public key output
 *
 * This function generates a static (persistent) key pair used for long-term
 * cryptographic operations in the RF Online authentication system.
 * Original Address: 0x1405F65A0
 */
void CryptoPP::AuthenticatedKeyAgreementDomain::GenerateStaticKeyPair(
    CryptoPP::AuthenticatedKeyAgreementDomain* this,
    struct CryptoPP::RandomNumberGenerator* randomGenerator,
    unsigned __int8* privateKey,
    unsigned __int8* publicKey) {

    // Local variables with meaningful names (original decompiled names in comments)
    CryptoPP::AuthenticatedKeyAgreementDomain* domain;     // Original: v4 ([sp+30h] [bp+8h])
    struct CryptoPP::RandomNumberGenerator* rng;           // Original: v5 ([sp+38h] [bp+10h])
    unsigned __int8* privKeyBuffer;                        // Original: v6 ([sp+40h] [bp+18h])
    unsigned __int8* pubKeyBuffer;                         // Original: v7 ([sp+48h] [bp+20h])

    // Initialize local variables
    pubKeyBuffer = publicKey;
    privKeyBuffer = privateKey;
    rng = randomGenerator;
    domain = this;

    // Call virtual function to generate the static key pair
    // First call the virtual destructor function
    ((void (*)(void))domain->vfptr[5].__vecDelDtor)();

    // Then call the key generation function
    ((void (__fastcall *)(CryptoPP::AuthenticatedKeyAgreementDomain *,
                          struct CryptoPP::RandomNumberGenerator *,
                          unsigned __int8 *,
                          unsigned __int8 *))domain->vfptr[5].Clone)(
        domain,
        rng,
        privKeyBuffer,
        pubKeyBuffer);
}
