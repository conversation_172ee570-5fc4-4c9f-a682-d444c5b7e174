/*
 * AsyncLogInfo.h
 * 
 * Header for asynchronous logging functionality in RF Online authentication
 * Restored from original decompiled source
 */

#pragma once

#include <cstdint>

// Forward declarations for RF Online types
class CNetCriticalSection;
class CMyTimer;

namespace RFOnline {
namespace Authentication {
namespace Logging {

/*
 * CAsyncLogInfo Class
 * Manages asynchronous logging operations for RF Online authentication system
 * Original: CAsyncLogInfo from decompiled authentication module
 */
class CAsyncLogInfo
{
public:
    // Constructor - initializes logging structure
    void __fastcall CAsyncLogInfo(CAsyncLogInfo *this);
    
    // Destructor - cleans up logging resources
    void __fastcall ~CAsyncLogInfo(CAsyncLogInfo *this);

public:
    // Member variables (preserved from original decompiled structure)
    int m_eType;                    // Log type enumeration
    uint32_t m_dwLogCount;          // Current log count
    __int64 m_szLogDirPath;         // Log directory path
    __int64 m_szLogFileName;        // Log file name
    __int64 m_szTypeName;           // Log type name
    CMyTimer* m_pkTimer;            // Timer for log operations
    CNetCriticalSection m_csLock;   // Critical section for thread safety
};

} // namespace Logging
} // namespace Authentication
} // namespace RFOnline
