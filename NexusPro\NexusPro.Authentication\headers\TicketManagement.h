/*
 * TicketManagement.h
 * 
 * Header for ticket management and authentication operations
 * Handles initialization, setting, and management of various authentication tickets
 */

#pragma once
#ifndef TICKETMANAGEMENT_H
#define TICKETMANAGEMENT_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Tickets {

// Forward declarations
class MiningTicket;

/**
 * Initialize authentication key ticket
 * Initializes the authentication key ticket system
 * @param this_ptr Pointer to MiningTicket object
 */
void __fastcall InitAuthKeyTicket(MiningTicket* this_ptr);

/**
 * Set authentication key ticket
 * Sets the authentication key ticket with specified parameters
 * @param this_ptr Pointer to MiningTicket object
 * @param ticket_data Ticket data to set
 * @param data_size Size of ticket data
 */
void __fastcall SetAuthKeyTicket(MiningTicket* this_ptr, void* ticket_data, unsigned int data_size);

/**
 * Set mining ticket authentication
 * Sets authentication data for mining ticket
 * @param this_ptr Pointer to MiningTicket object
 * @param auth_data Authentication data
 * @param auth_flags Authentication flags
 */
void __fastcall SetMiningTicketAuth(MiningTicket* this_ptr, void* auth_data, unsigned int auth_flags);

/**
 * Set mining ticket authentication data
 * Sets detailed authentication data for mining ticket
 * @param this_ptr Pointer to MiningTicket object
 * @param data_buffer Data buffer containing authentication information
 * @param buffer_size Size of the data buffer
 * @param validation_flags Validation flags for the data
 */
void __fastcall SetMiningTicketAuthData(MiningTicket* this_ptr, void* data_buffer, unsigned int buffer_size, unsigned int validation_flags);

} // namespace Tickets
} // namespace Authentication
} // namespace RFOnline

#endif // TICKETMANAGEMENT_H
