/*
 * AutoTradeSystem.h
 * 
 * Header for auto trade system authentication and management
 * Handles auto trade login, cancellation, tax rate notifications, and related operations
 */

#pragma once
#ifndef AUTOTRADESYSTEM_H
#define AUTOTRADESYSTEM_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace AutoTrade {

// Forward declarations
class CMgrAvatorItemHistory;
class TRC_AutoTrade;
class CRaceBuffInfoByHoly;

/**
 * Auto trade login sell function
 * Handles login operations for auto trade selling functionality
 * @param this_ptr Pointer to CMgrAvatorItemHistory object
 * @param user_data User data for auto trade
 * @param trade_data Trade-specific data
 */
void __fastcall AutoTradeLoginSell(CMgrAvatorItemHistory* this_ptr, void* user_data, void* trade_data);

/**
 * Cancel auto trade login
 * Handles cancellation of auto trade login operations
 * @param this_ptr Pointer to CMgrAvatorItemHistory object
 * @param session_id Session identifier
 * @param cancel_data Cancellation data
 */
void __fastcall LoginCancelAutoTrade(CMgrAvatorItemHistory* this_ptr, int session_id, void* cancel_data);

/**
 * Auto trade tax rate notification
 * Sends tax rate notifications for auto trade operations
 * @param this_ptr Pointer to TRC_AutoTrade object
 * @param tax_rate Current tax rate
 */
void __fastcall AutoTradeTaxRateNotify(TRC_AutoTrade* this_ptr, float tax_rate);

/**
 * Auto trade tax rate notification on login
 * Handles tax rate notifications during login process
 * @param this_ptr Pointer to TRC_AutoTrade object
 * @param session_id Session identifier
 * @param tax_data Tax rate data
 */
void __fastcall AutoTradeTaxRateNotifyLogin(TRC_AutoTrade* this_ptr, int session_id, void* tax_data);

/**
 * Notify race buff login
 * Handles race buff notifications during login
 * @param this_ptr Pointer to CRaceBuffInfoByHoly object
 * @param buff_data Buff information data
 */
void __fastcall NotifyRaceBuffLogin(CRaceBuffInfoByHoly* this_ptr, void* buff_data);

} // namespace AutoTrade
} // namespace Authentication
} // namespace RFOnline

#endif // AUTOTRADESYSTEM_H
