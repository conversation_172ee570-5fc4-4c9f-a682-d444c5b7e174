/**
 * @file ValidateECPParameters.h
 * @brief Header for ECP Parameter Validation Function
 * @note Original Function: ?ValidateParameters@ECP@CryptoPP@@QEBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x14060E2A0
 */

#pragma once
#ifndef VALIDATEECPPARAMETERS_H
#define VALIDATEECPPARAMETERS_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Cryptography {

/**
 * @brief Validates ECP (Elliptic Curve Prime) cryptographic parameters
 * @param this Pointer to ECP instance
 * @param randomGenerator Pointer to random number generator for validation
 * @param validationLevel Level of validation to perform (1=basic, 2=prime verification)
 * @return char Returns 1 if parameters are valid, 0 if invalid
 * 
 * This function performs comprehensive validation of elliptic curve parameters
 * used in cryptographic operations. It validates:
 * 1. Field size and parameter ranges
 * 2. Curve equation validity (4a³ + 27b² ≠ 0 mod p)
 * 3. Prime field verification (if validation level >= 2)
 */
char __fastcall ValidateECPParameters(
    CryptoPP::ECP* this,
    struct CryptoPP::RandomNumberGenerator* randomGenerator,
    unsigned int validationLevel);

} // namespace Cryptography
} // namespace Authentication
} // namespace RFOnline

#endif // VALIDATEECPPARAMETERS_H
