/*
 * DatabaseSystem.h
 * 
 * Header for database authentication and cash item system
 * Handles cash item database operations, user database functions, and related authentication
 */

#pragma once
#ifndef DATABASESYSTEM_H
#define DATABASESYSTEM_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Database {

// Forward declarations
class CRFCashItemDatabase;
class CUserDB;

/**
 * Cash item database authentication
 * Handles authentication for cash item database operations
 * @param this_ptr Pointer to CRFCashItemDatabase object
 * @param auth_data Authentication data
 * @param user_info User information
 * @param system_time System time information
 * @return Authentication result
 */
int __fastcall CashItemDatabaseAuth(CRFCashItemDatabase* this_ptr, void* auth_data, void* user_info, void* system_time);

/**
 * Japan cash item database authentication
 * Handles authentication for Japan-specific cash item database operations
 * @param this_ptr Pointer to CRFCashItemDatabase object
 * @param auth_data Authentication data
 * @param user_info User information
 * @param system_time System time information
 * @return Authentication result
 */
int __fastcall JapanCashItemDatabaseAuth(CRFCashItemDatabase* this_ptr, void* auth_data, void* user_info, void* system_time);

/**
 * Update trunk password
 * Updates the trunk password for user database
 * @param this_ptr Pointer to CUserDB object
 * @param new_password New password data
 * @return Update result
 */
bool __fastcall UpdateTrunkPassword(CUserDB* this_ptr, void* new_password);

} // namespace Database
} // namespace Authentication
} // namespace RFOnline

#endif // DATABASESYSTEM_H
