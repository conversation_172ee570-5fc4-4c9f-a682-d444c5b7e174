/**
 * @file ValidateImageBase.cpp
 * @brief RF Online PE Image Base Validation Function
 * @note Original Function: _ValidateImageBase
 * @note Original Address: 0x1404DE4C0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: _ValidateImageBase_1404DE4C0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// PE header constants
#define DOS_SIGNATURE 0x5A4D    // "MZ" - DOS header signature (23117 decimal)
#define PE_SIGNATURE  0x4550    // "PE" - PE header signature (17744 decimal)
#define IMAGE_FILE_MACHINE_AMD64 0x8664  // x64 machine type (34404 decimal)
#define IMAGE_FILE_MACHINE_I386  0x14c   // x86 machine type (332 decimal)
#define EXPECTED_MACHINE_TYPE 0x20B      // PE32+ format (523 decimal)

/**
 * @brief Validates the PE (Portable Executable) image base structure
 * @param pImageBase Pointer to the image base in memory
 * @return __int64 Returns 1 if valid PE image, 0 if invalid
 * 
 * This function performs basic validation of a PE file structure by checking:
 * 1. DOS header signature ("MZ")
 * 2. PE header signature ("PE")
 * 3. Machine type compatibility
 * 
 * This is used for security validation to ensure loaded modules are valid PE files.
 */
__int64 __fastcall ValidateImageBase(char* pImageBase) {
    __int64 result;                    // Original: result (rax register)
    char* peHeaderPtr;                 // Original: v2 ([sp+10h] [bp-18h])

    // Check DOS header signature (first 2 bytes should be "MZ" = 0x5A4D = 23117)
    if (*reinterpret_cast<WORD*>(pImageBase) == DOS_SIGNATURE) {
        // Get PE header offset from DOS header (at offset 0x3C)
        // DOS header + e_lfanew field points to PE header
        peHeaderPtr = &pImageBase[*reinterpret_cast<DWORD*>(pImageBase + 0x3C)];
        
        // Check PE header signature (should be "PE\0\0" = 0x4550 = 17744)
        if (*reinterpret_cast<DWORD*>(peHeaderPtr) == PE_SIGNATURE) {
            // Check machine type in COFF header (offset 4 from PE signature)
            // For PE32+, the machine type should be 0x20B (523 decimal)
            WORD machineType = *reinterpret_cast<WORD*>(peHeaderPtr + 24);
            result = (machineType == EXPECTED_MACHINE_TYPE) ? 1 : 0;
        } else {
            // Invalid PE signature
            result = 0;
        }
    } else {
        // Invalid DOS signature
        result = 0;
    }
    
    return result;
}
