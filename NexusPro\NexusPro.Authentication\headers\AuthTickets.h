/*
 * AuthTickets.h
 * 
 * Header for authentication ticket management functions
 * Handles critical, mental, and mining ticket authentication operations
 */

#pragma once
#ifndef AUTHTICKETS_H
#define AUTHTICKETS_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Tickets {

// Forward declarations
class MiningTicket;

/**
 * Authenticate critical ticket for last operation
 * @param this_ptr Pointer to MiningTicket object
 * @param byCurrentYear Current year value
 * @param byCurrentMonth Current month value
 * @param byCurrentDay Current day value
 * @param byCurrentHour Current hour value
 * @param byNumOfTime Number of time units
 * @return Authentication result (__int64)
 */
__int64 AuthLastCriTicket(MiningTicket* this_ptr, unsigned __int16 byCurrentYear, char byCurrentMonth, char byCurrentDay, char byCurrentHour, char byNumOfTime);

/**
 * Authenticate mental ticket for last operation
 * @param this_ptr Pointer to MiningTicket object
 * @param wYear Year value
 * @param byMonth Month value
 * @param byDay Day value
 * @param byHour Hour value
 * @param byNumofTime Number of times used
 * @return Authentication result
 */
int __fastcall AuthLastMentalTicket(MiningTicket* this_ptr, unsigned __int16 wYear, char byMonth, char byDay, char byHour, char byNumofTime);

/**
 * Authenticate mining ticket
 * @param this_ptr Pointer to CHolyStoneSystem object
 * @param dwSerial Serial number for authentication
 * @return Authentication result
 */
bool __fastcall AuthMiningTicket(void* this_ptr, unsigned int dwSerial);

/**
 * Check equality of authentication key tickets
 * @param this_ptr Pointer to MiningTicket object
 * @param other_ptr Pointer to other MiningTicket object for comparison
 * @return True if tickets are equal, false otherwise
 */
bool __fastcall AuthKeyTicketEquals(MiningTicket* this_ptr, const MiningTicket* other_ptr);

/**
 * Check inequality of authentication key tickets
 * @param this_ptr Pointer to MiningTicket object
 * @param other_ptr Pointer to other MiningTicket object for comparison
 * @return True if tickets are not equal, false otherwise
 */
bool __fastcall AuthKeyTicketNotEquals(MiningTicket* this_ptr, const MiningTicket* other_ptr);

} // namespace Tickets
} // namespace Authentication
} // namespace RFOnline

#endif // AUTHTICKETS_H
