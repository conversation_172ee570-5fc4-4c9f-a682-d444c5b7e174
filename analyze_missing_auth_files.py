#!/usr/bin/env python3
"""
Script to analyze missing files in Authentication module
Compares original decompiled source with converted files
"""

import os
import glob
from pathlib import Path

def get_file_list(directory, extension):
    """Get list of files with given extension in directory"""
    pattern = os.path.join(directory, f"*.{extension}")
    files = glob.glob(pattern)
    return [os.path.basename(f) for f in files]

def categorize_files(filenames):
    """Categorize files by their naming patterns"""
    categories = {
        'constructors': [],
        'destructors': [],
        'jump_functions': [],
        'stl_containers': [],
        'async_log': [],
        'crypto_validation': [],
        'dialog_functions': [],
        'invalidation': [],
        'other': []
    }
    
    for filename in filenames:
        name_lower = filename.lower()
        
        if filename.startswith('0') and 'QEAA' in filename:
            categories['constructors'].append(filename)
        elif filename.startswith('1') and 'QEAA' in filename:
            categories['destructors'].append(filename)
        elif filename.startswith('j_'):
            categories['jump_functions'].append(filename)
        elif 'asynclog' in name_lower:
            categories['async_log'].append(filename)
        elif any(x in name_lower for x in ['stl', 'vector', 'list', 'map', 'iterator']):
            categories['stl_containers'].append(filename)
        elif any(x in name_lower for x in ['dlgroup', 'ecp', 'ec2n', 'validation']):
            categories['crypto_validation'].append(filename)
        elif 'dialog' in name_lower:
            categories['dialog_functions'].append(filename)
        elif 'invalidat' in name_lower:
            categories['invalidation'].append(filename)
        else:
            categories['other'].append(filename)
    
    return categories

def main():
    # Paths
    original_path = "Decompiled Source Code - IDA Pro/authentication"
    converted_path = "NexusPro/NexusPro.Authentication/source"
    
    print("🔍 AUTHENTICATION MODULE FILE ANALYSIS")
    print("=" * 50)
    
    # Get file lists
    original_files = get_file_list(original_path, "c")
    converted_files = get_file_list(converted_path, "cpp")
    
    print(f"📊 FILE COUNTS:")
    print(f"   Original .c files: {len(original_files)}")
    print(f"   Converted .cpp files: {len(converted_files)}")
    print(f"   Missing files: {len(original_files) - len(converted_files)}")
    print(f"   Data loss: {((len(original_files) - len(converted_files)) / len(original_files) * 100):.1f}%")
    print()
    
    # Categorize original files
    categories = categorize_files(original_files)
    
    print("📂 ORIGINAL FILE CATEGORIES:")
    for category, files in categories.items():
        if files:
            print(f"   {category.replace('_', ' ').title()}: {len(files)} files")
    print()
    
    # Find missing files (simplified check by count)
    print("🚨 CRITICAL MISSING CATEGORIES:")
    for category, files in categories.items():
        if len(files) > 5:  # Categories with significant file counts
            print(f"   {category.replace('_', ' ').title()}: {len(files)} files missing")
    print()
    
    # Sample files from each major category
    print("📋 SAMPLE FILES BY CATEGORY:")
    for category, files in categories.items():
        if files and len(files) > 2:
            print(f"\n   {category.replace('_', ' ').title()} (showing first 3):")
            for i, file in enumerate(files[:3]):
                print(f"     {i+1}. {file}")
    
    print("\n" + "=" * 50)
    print("⚠️  RECOMMENDATION: Restore missing files systematically")
    print("   Priority: constructors, destructors, async_log, crypto_validation")

if __name__ == "__main__":
    main()
