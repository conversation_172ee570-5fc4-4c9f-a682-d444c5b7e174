/**
 * @file BillingIDLogin.cpp
 * @brief RF Online Billing ID Login Function
 * @note Original Function: ?Login@CBillingID@@UEAAXPEAVCUserDB@@@Z
 * @note Original Address: 0x14028E0F0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: LoginCBillingIDUEAAXPEAVCUserDBZ_14028E0F0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <winsock2.h>

/**
 * @brief Initiates login process for billing ID authentication
 * @param this Pointer to CBillingID instance
 * @param pUserDB Pointer to user database containing login information
 * 
 * This function handles the login process for billing ID authentication,
 * checking if the user is from a PC Bang and processing the billing information
 * accordingly. It validates the user's billing status and sends appropriate
 * login messages to the billing system.
 */
void __fastcall CBillingID::Login(CBillingID* this, CUserDB* pUserDB) {
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;            // Original: v2 (rdi register)
    signed __int64 loopCounter;        // Original: i (rcx register)
    char* ipAddressString;             // Original: v4 (rax register)
    __int64 stackBuffer[24];           // Original: v5 (stack buffer [sp+0h] [bp-68h])
    __int16 billingType;               // Original: v6 ([sp+20h] [bp-48h])
    _SYSTEMTIME* endDatePtr;           // Original: v7 ([sp+28h] [bp-40h])
    int remainingTime;                 // Original: v8 ([sp+30h] [bp-38h])
    _SYSTEMTIME* endDateCopy;          // Original: v9 ([sp+40h] [bp-28h])
    char* cmsData;                     // Original: v10 ([sp+48h] [bp-20h])
    CBillingVtbl* virtualTable;        // Original: v11 ([sp+50h] [bp-18h])
    CBillingID* billingInstance;       // Original: v12 ([sp+70h] [bp+8h])
    CUserDB* userDatabase;             // Original: v13 ([sp+78h] [bp+10h])

    // Initialize local variables
    userDatabase = pUserDB;
    billingInstance = this;
    bufferPointer = stackBuffer;
    
    // Initialize memory buffer with debug pattern (0xCCCCCCCC)
    for (loopCounter = 24LL; loopCounter; --loopCounter) {
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        bufferPointer = reinterpret_cast<__int64*>(reinterpret_cast<char*>(bufferPointer) + 4);
    }
    
    // Check if user is from a PC Bang (internet cafe)
    if (pUserDB->m_BillingInfo.bIsPcBang) {
        // Set up billing information pointers
        endDateCopy = &pUserDB->m_BillingInfo.stEndDate;
        cmsData = pUserDB->m_BillingInfo.szCMS;
        
        // Convert user's IP address to string format
        ipAddressString = inet_ntoa(reinterpret_cast<struct in_addr>(pUserDB->m_dwIP));
        
        // Get virtual function table and prepare billing data
        virtualTable = billingInstance->vfptr;
        remainingTime = userDatabase->m_BillingInfo.lRemainTime;
        endDatePtr = endDateCopy;
        billingType = userDatabase->m_BillingInfo.iType;
        
        // Call virtual function to send login message
        // This calls the SendMsg_Login function through the virtual table
        reinterpret_cast<void (__fastcall *)(CBillingID*, signed __int64, char*, char*)>(
            virtualTable->SendMsg_Login)(
                billingInstance,
                reinterpret_cast<signed __int64>(userDatabase->m_szAccountID),
                ipAddressString,
                cmsData);
        
        // Set billing status to indicate no logout required
        CUserDB::SetBillingNoLogout(userDatabase, 0);
    }
    // If not a PC Bang user, no special billing processing is needed
}
