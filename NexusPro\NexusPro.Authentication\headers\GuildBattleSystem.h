/*
 * GuildBattleSystem.h
 * 
 * Header for guild battle authentication and login functionality
 * Handles guild battle member authentication, manager login, and normal guild battle operations
 */

#pragma once
#ifndef GUILDBATTLESYSTEM_H
#define GUILDBATTLESYSTEM_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace GuildBattle {

// Forward declarations
class CGuild;
class CGuildMember;
class CGuildBattleManager;
struct GUILD_BATTLE;

/**
 * Guild battle guild login function
 * Handles login operations for guild battle participants
 * @param this_ptr Pointer to GUILD_BATTLE object
 * @param guild_id Guild identifier
 * @param member_count Number of members
 * @param member_data Pointer to member data
 * @param additional_param Additional parameter
 */
void __fastcall GuildBattleGuildLogin(GUILD_BATTLE* this_ptr, int guild_id, int member_count, void* member_data, void* additional_param);

/**
 * Guild battle manager login function
 * Handles login operations for guild battle managers
 * @param this_ptr Pointer to GUILD_BATTLE object
 * @param manager_id Manager identifier
 * @param member_count Number of members
 * @param member_data Pointer to member data
 */
void __fastcall GuildBattleManagerLogin(GUILD_BATTLE* this_ptr, int manager_id, int member_count, void* member_data);

/**
 * Guild battle member login function
 * Handles login operations for individual guild battle members
 * @param this_ptr Pointer to GUILD_BATTLE object
 * @param member_id Member identifier
 * @param guild_id Guild identifier
 * @param battle_data Battle-specific data
 */
void __fastcall GuildBattleMemberLogin(GUILD_BATTLE* this_ptr, int member_id, int guild_id, void* battle_data);

/**
 * Normal guild battle login function
 * Handles standard guild battle login operations
 * @param this_ptr Pointer to GUILD_BATTLE object
 * @param param1 First parameter
 * @param param2 Second parameter
 * @param param3 Third parameter
 */
void __fastcall NormalGuildBattleLogin(GUILD_BATTLE* this_ptr, int param1, int param2, int param3);

} // namespace GuildBattle
} // namespace Authentication
} // namespace RFOnline

#endif // GUILDBATTLESYSTEM_H
