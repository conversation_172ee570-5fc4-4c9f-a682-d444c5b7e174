/*
 * CryptoValidation.h
 * 
 * Header for critical cryptographic validation functions
 * Restored from original decompiled RF Online authentication source
 */

#pragma once

#include <cstdint>

namespace RFOnline {
namespace Authentication {
namespace Crypto {

// EC2N Point Group Parameter Validation Functions
// These functions are critical for cryptographic security in RF Online

/*
 * Validates EC2N point group parameters for cryptographic operations
 * Original: ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAD0.c
 */
int __fastcall ValidateEC2NGroupParameters(__int64 a1);

/*
 * Alternative EC2N point validation with different parameters  
 * Original: ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADAF0.c
 */
int __fastcall ValidateEC2NGroupParametersAlt(__int64 a1, __int64 a2);

/*
 * Extended EC2N point validation with additional security checks
 * Original: ValidateDL_GroupParametersUEC2NPointCryptoPPCrypto_1405ADD90.c
 */
int __fastcall ValidateEC2NGroupParametersExtended(__int64 a1, __int64 a2, uint32_t flags);

/*
 * HMAC MessageAuthenticationCodeImpl Constructor
 * Original: 0MessageAuthenticationCodeImplVHMAC_BaseCryptoPPVH_140465820.c
 */
void __fastcall HMACMessageAuthCodeConstructor(void* this_ptr);

} // namespace Crypto
} // namespace Authentication
} // namespace RFOnline
