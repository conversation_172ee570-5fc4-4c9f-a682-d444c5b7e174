/**
 * @file ValidateImageBase.h
 * @brief Header for PE Image Base Validation Function
 * @note Original Function: _ValidateImageBase
 * @note Original Address: 0x1404DE4C0
 */

#pragma once
#ifndef VALIDATEIMAGEBASE_H
#define VALIDATEIMAGEBASE_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Security {

/**
 * @brief Validates the PE (Portable Executable) image base structure
 * @param pImageBase Pointer to the image base in memory
 * @return __int64 Returns 1 if valid PE image, 0 if invalid
 * 
 * This function performs basic validation of a PE file structure by checking:
 * 1. DOS header signature ("MZ")
 * 2. PE header signature ("PE")
 * 3. Machine type compatibility
 */
__int64 __fastcall ValidateImageBase(char* pImageBase);

} // namespace Security
} // namespace Authentication
} // namespace RFOnline

#endif // VALIDATEIMAGEBASE_H
