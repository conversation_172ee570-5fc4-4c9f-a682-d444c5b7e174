/*
 * DialogOccFunctions.h
 * 
 * Header for OCC (OLE Control Container) dialog functions
 * Restored from original decompiled RF Online authentication source
 */

#pragma once
#ifndef DIALOGOCCFUNCTIONS_H
#define DIALOGOCCFUNCTIONS_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace UI {

// Forward declarations for AFX structures and classes
struct _AFX_OCC_DIALOG_INFO;
class CDialog;
class CFormView;
class CWnd;

/**
 * Get OCC dialog info for CDialog
 * @param this_ptr Pointer to CDialog object
 * @return Pointer to AFX OCC dialog info structure
 */
struct _AFX_OCC_DIALOG_INFO* __fastcall GetDialogOccInfo(CDialog* this_ptr);

/**
 * Get OCC dialog info for CFormView
 * @param this_ptr Pointer to CFormView object
 * @return Pointer to AFX OCC dialog info structure
 */
struct _AFX_OCC_DIALOG_INFO* __fastcall GetFormViewOccInfo(CFormView* this_ptr);

/**
 * Get OCC dialog info for CWnd
 * @param this_ptr Pointer to CWnd object
 * @return Pointer to AFX OCC dialog info structure
 */
struct _AFX_OCC_DIALOG_INFO* __fastcall GetWndOccInfo(CWnd* this_ptr);

/**
 * Set OCC dialog info for CDialog
 * @param this_ptr Pointer to CDialog object
 * @param info_ptr Pointer to AFX OCC dialog info structure
 * @return Success/failure result
 */
int __fastcall SetDialogOccInfo(CDialog* this_ptr, struct _AFX_OCC_DIALOG_INFO* info_ptr);

/**
 * Set OCC dialog info for CFormView
 * @param this_ptr Pointer to CFormView object
 * @param info_ptr Pointer to AFX OCC dialog info structure
 * @return Success/failure result
 */
int __fastcall SetFormViewOccInfo(CFormView* this_ptr, struct _AFX_OCC_DIALOG_INFO* info_ptr);

/**
 * Set OCC dialog info for CWnd
 * @param this_ptr Pointer to CWnd object
 * @param info_ptr Pointer to AFX OCC dialog info structure
 * @return Success/failure result
 */
int __fastcall SetWndOccInfo(CWnd* this_ptr, struct _AFX_OCC_DIALOG_INFO* info_ptr);

} // namespace UI
} // namespace Authentication
} // namespace RFOnline

#endif // DIALOGOCCFUNCTIONS_H
