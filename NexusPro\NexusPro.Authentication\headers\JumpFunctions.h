/*
 * JumpFunctions.h
 * 
 * Header for critical jump function thunks in RF Online authentication
 * These functions provide call redirects and optimization wrappers
 * Restored from original decompiled source (221 jump functions)
 */

#pragma once

#include <cstdint>

// Forward declarations
class CAsyncLogInfo;

namespace RFOnline {
namespace Authentication {
namespace JumpFunctions {

// ============================================================================
// ASYNC LOG INFO JUMP FUNCTIONS
// ============================================================================

// Jump function for CAsyncLogInfo constructor
void __fastcall j_CAsyncLogInfo_Constructor(CAsyncLogInfo *this);

// Jump function for CAsyncLogInfo destructor
void __fastcall j_CAsyncLogInfo_Destructor(CAsyncLogInfo *this);

// Jump functions for CAsyncLogInfo methods
uint32_t __fastcall j_GetCountCAsyncLogInfo();
const char* __fastcall j_GetDirPathCAsyncLogInfo();
const char* __fastcall j_GetFileNameCAsyncLogInfo();
const char* __fastcall j_GetTypeNameCAsyncLogInfo();
void __fastcall j_IncreaseCountCAsyncLogInfo();

// ============================================================================
// CRYPTOGRAPHIC VALIDATION JUMP FUNCTIONS
// ============================================================================

// Jump functions for cryptographic validation
int __fastcall j_ValidateDL_GroupParametersECPPoint(__int64 a1);
int __fastcall j_ValidateDL_GroupParametersEC2NPoint(__int64 a1);

// ============================================================================
// AUTHENTICATION TICKET JUMP FUNCTIONS
// ============================================================================

// Jump functions for authentication tickets
void __fastcall j_Init_AuthKeyTicketMiningTicket();
void __fastcall j_Set_AuthKeyTicketMiningTicket(uint32_t param1, uint8_t param2, uint8_t param3, uint8_t param4, uint8_t param5);

// ============================================================================
// BILLING SYSTEM JUMP FUNCTIONS
// ============================================================================

// Jump functions for billing system
void __fastcall j_LoginCBillingManager(void* userDB);
void __fastcall j_LoginCBillingID(void* userDB);
void __fastcall j_LoginCBillingJP(void* userDB);

// ============================================================================
// NETWORK SESSION JUMP FUNCTIONS
// ============================================================================

// Jump functions for network sessions
void __fastcall j_OnConnectSessionCHackShieldExSystem(uint16_t sessionId);
void __fastcall j_OnDisConnectSessionCHackShieldExSystem(uint16_t sessionId);

} // namespace JumpFunctions
} // namespace Authentication
} // namespace RFOnline
