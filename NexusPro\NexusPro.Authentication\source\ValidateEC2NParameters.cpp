/**
 * @file ValidateEC2NParameters.cpp
 * @brief RF Online EC2N (Elliptic Curve over GF(2^n)) Parameter Validation Function
 * @note Original Function: ?ValidateParameters@EC2N@CryptoPP@@QEBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x14062E210
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: ValidateParametersEC2NCryptoPPQEBA_NAEAVRandomNumb_14062E210.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * @brief Validates EC2N (Elliptic Curve over GF(2^n)) cryptographic parameters
 * @param this Pointer to EC2N instance
 * @param randomGenerator Pointer to random number generator for validation
 * @param validationLevel Level of validation to perform (1=basic, 2=advanced)
 * @return bool Returns true if parameters are valid, false if invalid
 * 
 * This function performs validation of elliptic curve parameters over binary fields GF(2^n).
 * It validates:
 * 1. Curve coefficient 'b' is non-zero (required for non-singular curve)
 * 2. Coefficient bit lengths are within field limits
 * 3. Field polynomial irreducibility (if validation level >= 1)
 * 
 * EC2N curves are defined by the equation: y² + xy = x³ + ax² + b over GF(2^n)
 */
bool __fastcall CryptoPP::EC2N::ValidateParameters(
    CryptoPP::EC2N* this,
    struct CryptoPP::RandomNumberGenerator* randomGenerator,
    unsigned int validationLevel) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    unsigned int aCoefficientCount;        // Original: v3 (ST28_4)
    CryptoPP::GF2NP* fieldPointer1;       // Original: v4 (rax)
    unsigned int bCoefficientCount;        // Original: v5 (ST30_4)
    CryptoPP::GF2NP* fieldPointer2;       // Original: v6 (rax)
    __int64 fieldReference;                // Original: v7 (rax)
    CryptoPP::PolynomialMod2* modulus;     // Original: v8 (rax)
    bool basicValidation;                  // Original: v10 ([sp+20h] [bp-28h])
    bool coefficientAValid;                // Original: v11 ([sp+2Ch] [bp-1Ch])
    bool coefficientBValid;                // Original: v12 ([sp+34h] [bp-14h])
    bool advancedValidation;               // Original: v13 ([sp+38h] [bp-10h])
    CryptoPP::EC2N* ec2nInstance;          // Original: v14 ([sp+50h] [bp+8h])
    unsigned int level;                    // Original: v15 ([sp+60h] [bp+18h])

    // Initialize local variables
    level = validationLevel;
    ec2nInstance = this;
    
    // Validate coefficient 'a' and 'b' constraints
    // First check: coefficient 'b' must be non-zero (non-singular curve requirement)
    // Second check: coefficient 'a' bit count must be within field limits
    coefficientAValid = !CryptoPP::PolynomialMod2::operator!(&this->m_b.reg)
        && (aCoefficientCount = CryptoPP::PolynomialMod2::CoefficientCount(&ec2nInstance->m_a),
            fieldPointer1 = reinterpret_cast<CryptoPP::GF2NP*>(
                CryptoPP::member_ptr<CryptoPP::GF2NP>::operator->(
                    reinterpret_cast<__int64>(&ec2nInstance->m_field))),
            aCoefficientCount <= static_cast<unsigned int>(
                CryptoPP::GF2NP::MaxElementBitLength(fieldPointer1)));
    
    // Validate coefficient 'b' bit count is within field limits
    coefficientBValid = coefficientAValid
        && (bCoefficientCount = CryptoPP::PolynomialMod2::CoefficientCount(&ec2nInstance->m_b),
            fieldPointer2 = reinterpret_cast<CryptoPP::GF2NP*>(
                CryptoPP::member_ptr<CryptoPP::GF2NP>::operator->(
                    reinterpret_cast<__int64>(&ec2nInstance->m_field))),
            bCoefficientCount <= static_cast<unsigned int>(
                CryptoPP::GF2NP::MaxElementBitLength(fieldPointer2)));
    
    basicValidation = coefficientBValid;
    
    // If validation level >= 1, perform advanced validation
    if (level >= 1) {
        // Validate that the field polynomial is irreducible
        // This is crucial for the security of the elliptic curve
        advancedValidation = coefficientBValid
            && (fieldReference = CryptoPP::member_ptr<CryptoPP::GF2NP>::operator->(
                    reinterpret_cast<__int64>(&ec2nInstance->m_field)),
                modulus = reinterpret_cast<CryptoPP::PolynomialMod2*>(
                    CryptoPP::QuotientRing<CryptoPP::EuclideanDomainOf<CryptoPP::PolynomialMod2>>::GetModulus(fieldReference)),
                CryptoPP::PolynomialMod2::IsIrreducible(modulus));
        
        basicValidation = advancedValidation;
    }
    
    return basicValidation;
}
