/*
 * AccountServerLogin.h
 * 
 * Header for account server login functionality in RF Online authentication
 * Handles main thread login operations for account server connections
 */

#pragma once
#ifndef ACCOUNTSERVERLOGIN_H
#define ACCOUNTSERVERLOGIN_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Server {

// Forward declarations
class CMainThread;

/**
 * Account server login main thread function
 * Handles the primary login process for account server connections
 * @param this_ptr Pointer to CMainThread object
 */
void AccountServerLogin(CMainThread* this_ptr);

} // namespace Server
} // namespace Authentication
} // namespace RFOnline

#endif // ACCOUNTSERVERLOGIN_H
