/*
 * AsyncLogInfo.cpp
 * 
 * Critical asynchronous logging functionality for RF Online authentication
 * Restored from original decompiled source
 * 
 * Original functions:
 * - 0CAsyncLogInfoQEAAXZ_1403BC9F0.c (Constructor)
 * - 1CAsyncLogInfoQEAAXZ_1403BCA80.c (Destructor)
 */

#include "../headers/AsyncLogInfo.h"
#include <cstdint>
#include <cstring>

namespace RFOnline {
namespace Authentication {
namespace Logging {

/*
 * CAsyncLogInfo Constructor
 * Address: 0x1403BC9F0
 * Purpose: Initializes asynchronous logging information structure
 */
void __fastcall CAsyncLogInfo::CAsyncLogInfo(CAsyncLogInfo *this)
{
    __int64 *v1;           // rdi@1
    signed __int64 i;      // rcx@1
    __int64 v3;            // [sp+0h] [bp-28h]@1
    CAsyncLogInfo *v4;     // [sp+30h] [bp+8h]@1

    // Preserve original decompiled logic exactly
    v4 = this;
    v1 = &v3;
    
    // Initialize memory with debug pattern (original IDA pattern)
    for (i = 8i64; i; --i)
    {
        *reinterpret_cast<uint32_t*>(v1) = 0xCCCCCCCC; // -858993460 in hex
        v1 = reinterpret_cast<__int64*>(reinterpret_cast<char*>(v1) + 4);
    }
    
    // Initialize member variables
    v4->m_eType = -1;
    v4->m_dwLogCount = 0;
    v4->m_szLogDirPath = 0i64;
    v4->m_szLogFileName = 0i64;
    v4->m_szTypeName = 0i64;
    v4->m_pkTimer = 0i64;
    
    // Initialize critical section
    CNetCriticalSection::CNetCriticalSection(&v4->m_csLock);
}

/*
 * CAsyncLogInfo Destructor  
 * Address: 0x1403BCA80
 * Purpose: Properly cleans up asynchronous logging resources
 */
void __fastcall CAsyncLogInfo::~CAsyncLogInfo(CAsyncLogInfo *this)
{
    __int64 *v1;           // rdi@1
    signed __int64 i;      // rcx@1
    __int64 v3;            // rax@5
    __int64 v4;            // [sp+0h] [bp-68h]@1
    void *v5;              // [sp+20h] [bp-48h]@4
    void *v6;              // [sp+28h] [bp-40h]@4
    void *v7;              // [sp+30h] [bp-38h]@4
    CMyTimer *v8;          // [sp+38h] [bp-30h]@4
    CMyTimer *v9;          // [sp+40h] [bp-28h]@4
    __int64 v10;           // [sp+48h] [bp-20h]@4
    __int64 v11;           // [sp+50h] [bp-18h]@5
    CAsyncLogInfo *v12;    // [sp+70h] [bp+8h]@1

    // Preserve original decompiled logic exactly
    v12 = this;
    v1 = &v4;
    
    // Initialize memory with debug pattern
    for (i = 24i64; i; --i)
    {
        *reinterpret_cast<uint32_t*>(v1) = 0xCCCCCCCC; // -858993460 in hex
        v1 = reinterpret_cast<__int64*>(reinterpret_cast<char*>(v1) + 4);
    }
    
    v10 = -2i64;
    
    // Clean up allocated string resources
    v5 = v12->m_szLogDirPath;
    operator delete[](v5);
    
    v6 = v12->m_szLogFileName;
    operator delete[](v6);
    
    v7 = v12->m_szTypeName;
    operator delete[](v7);
    
    // Clean up timer resource
    v9 = v12->m_pkTimer;
    v8 = v9;
    if (v9)
    {
        v3 = reinterpret_cast<__int64>(
            reinterpret_cast<int(__fastcall*)(CMyTimer*, signed __int64)>(
                v8->vfptr->__vecDelDtor
            )(v8, 1i64)
        );
        v11 = v3;
    }
    else
    {
        v11 = 0i64;
    }
    
    // Clean up critical section
    CNetCriticalSection::~CNetCriticalSection(&v12->m_csLock);
}

} // namespace Logging
} // namespace Authentication
} // namespace RFOnline
