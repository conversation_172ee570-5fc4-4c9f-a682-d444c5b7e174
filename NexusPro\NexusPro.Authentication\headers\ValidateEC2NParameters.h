/**
 * @file ValidateEC2NParameters.h
 * @brief Header for EC2N Parameter Validation Function
 * @note Original Function: ?ValidateParameters@EC2N@CryptoPP@@QEBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x14062E210
 */

#pragma once
#ifndef VALIDATEEC2NPARAMETERS_H
#define VALIDATEEC2NPARAMETERS_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Cryptography {

/**
 * @brief Validates EC2N (Elliptic Curve over GF(2^n)) cryptographic parameters
 * @param this Pointer to EC2N instance
 * @param randomGenerator Pointer to random number generator for validation
 * @param validationLevel Level of validation to perform (1=basic, 2=advanced)
 * @return bool Returns true if parameters are valid, false if invalid
 * 
 * This function performs validation of elliptic curve parameters over binary fields GF(2^n).
 * It validates:
 * 1. Curve coefficient 'b' is non-zero (required for non-singular curve)
 * 2. Coefficient bit lengths are within field limits
 * 3. Field polynomial irreducibility (if validation level >= 1)
 */
bool __fastcall ValidateEC2NParameters(
    CryptoPP::EC2N* this,
    struct CryptoPP::RandomNumberGenerator* randomGenerator,
    unsigned int validationLevel);

} // namespace Cryptography
} // namespace Authentication
} // namespace RFOnline

#endif // VALIDATEEC2NPARAMETERS_H
