/*
 * MessageSystem.h
 * 
 * Header for message sending and communication system
 * Handles various login messages, user notifications, and guild communications
 */

#pragma once
#ifndef MESSAGESYSTEM_H
#define MESSAGESYSTEM_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Messaging {

// Forward declarations
class CBilling;
class CBillingID;
class CBillingJP;
class CBillingNULL;
class CBillingI;
class CGuild;

/**
 * Send all user login message
 * Broadcasts login message to all users
 * @param this_ptr Pointer to CBillingI object
 */
void __fastcall SendAllUserLogin(CBillingI* this_ptr);

/**
 * Send billing ID login message
 * Sends login message for billing ID authentication
 * @param this_ptr Pointer to CBillingID object
 * @param message_data Message data
 * @param flags Message flags
 * @param system_time System time
 * @param additional_data Additional message data
 * @return Send result
 */
bool __fastcall SendBillingIDLogin(CBillingID* this_ptr, void* message_data, int flags, void* system_time, void* additional_data);

/**
 * Send Japan billing login message
 * Sends login message for Japan billing authentication
 * @param this_ptr Pointer to CBillingJP object
 * @param message_data Message data
 * @param flags Message flags
 * @param system_time System time
 * @param additional_data Additional message data
 * @return Send result
 */
bool __fastcall SendJapanBillingLogin(CBillingJP* this_ptr, void* message_data, int flags, void* system_time, void* additional_data);

/**
 * Send standard billing login message
 * Sends login message for standard billing authentication
 * @param this_ptr Pointer to CBilling object
 * @param message_data Message data
 * @param flags Message flags
 * @param system_time System time
 * @param additional_data Additional message data
 * @return Send result
 */
bool __fastcall SendLoginMessage(CBilling* this_ptr, void* message_data, int flags, void* system_time, void* additional_data);

/**
 * Send NULL billing login message
 * Sends login message for NULL billing (free server) authentication
 * @param this_ptr Pointer to CBillingNULL object
 * @param message_data Message data
 * @param flags Message flags
 * @param system_time System time
 * @param additional_data Additional message data
 * @return Send result
 */
bool __fastcall SendNullBillingLogin(CBillingNULL* this_ptr, void* message_data, int flags, void* system_time, void* additional_data);

/**
 * Send guild member login message
 * Sends login notification to guild members
 * @param this_ptr Pointer to CGuild object
 * @param member_id Member identifier
 * @param guild_data Guild-specific data
 * @param login_data Login information
 */
void __fastcall SendGuildMemberLogin(CGuild* this_ptr, unsigned int member_id, void* guild_data, void* login_data);

} // namespace Messaging
} // namespace Authentication
} // namespace RFOnline

#endif // MESSAGESYSTEM_H
