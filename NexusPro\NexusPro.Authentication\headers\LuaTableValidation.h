/*
 * LuaTableValidation.h
 * 
 * Header for Lua table validation functionality
 * Handles validation of Lua table objects used in RF Online scripting system
 */

#pragma once
#ifndef LUATABLEVALIDATION_H
#define LUATABLEVALIDATION_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations for Lua types
extern "C" {
    typedef struct lua_State lua_State;
    const void* lua_topointer(lua_State* L, int idx);
    int lua_type(lua_State* L, int idx);
}

namespace lua_tinker {
    struct table_obj {
        lua_State* m_L;        // Lua state pointer
        int m_index;           // Index in Lua stack
        const void* m_pointer; // Cached pointer to table object
    };
}

namespace RFOnline {
namespace Authentication {
namespace Scripting {

/**
 * Validate Lua table object
 * Checks if a Lua table object is still valid and accessible
 * @param this_ptr Pointer to lua_tinker::table_obj
 * @return True if table is valid, false otherwise
 */
bool __fastcall ValidateLuaTable(lua_tinker::table_obj* this_ptr);

} // namespace Scripting
} // namespace Authentication
} // namespace RFOnline

#endif // LUATABLEVALIDATION_H
